
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/level/LevelPageController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c8e13uMxpxGELez69T2SfJx', 'LevelPageController');
// scripts/level/LevelPageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var LeaveDialogController_1 = require("../hall/LeaveDialogController");
var Tools_1 = require("../util/Tools");
var Config_1 = require("../util/Config");
var GlobalManagerController_1 = require("../GlobalManagerController");
var SingleChessBoardController_1 = require("../game/Chess/SingleChessBoardController");
var HexSingleChessBoardController_1 = require("../game/Chess/HexSingleChessBoardController");
var WebSocketManager_1 = require("../net/WebSocketManager");
var MessageId_1 = require("../net/MessageId");
var GameMgr_1 = require("../common/GameMgr");
var EventCenter_1 = require("../common/EventCenter");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var LevelPageController = /** @class */ (function (_super) {
    __extends(LevelPageController, _super);
    function LevelPageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        // 返回按钮
        _this.backButton = null;
        // 开始游戏按钮
        _this.startGameButton = null;
        // 地雷数UI标签
        _this.mineCountLabel = null;
        // 当前关卡数UI标签
        _this.currentLevelLabel = null;
        // 退出游戏弹窗
        _this.leaveDialogController = null;
        // level_page节点
        _this.levelPageNode = null;
        // game_map_1节点
        _this.gameMap1Node = null;
        // game_map_2节点
        _this.gameMap2Node = null;
        // 方形地图节点引用
        _this.qipan8x8Node = null; // level_page/game_map_1/chess_bg/qipan8*8
        _this.qipan8x9Node = null; // level_page/game_map_1/chess_bg/qipan8*9
        _this.qipan9x9Node = null; // level_page/game_map_1/chess_bg/qipan9*9
        _this.qipan9x10Node = null; // level_page/game_map_1/chess_bg/qipan9*10
        _this.qipan10x10Node = null; // level_page/game_map_1/chess_bg/qipan10*10
        // 特殊关卡节点引用
        _this.levelS001Node = null; // level_page/game_map_2/game_bg/Level_S001
        _this.levelS002Node = null; // level_page/game_map_2/game_bg/Level_S002
        _this.levelS003Node = null; // level_page/game_map_2/game_bg/Level_S003
        _this.levelS004Node = null; // level_page/game_map_2/game_bg/Level_S004
        _this.levelS005Node = null; // level_page/game_map_2/game_bg/Level_S005
        _this.levelS006Node = null; // level_page/game_map_2/game_bg/Level_S006
        // 单机模式棋盘控制器
        _this.singleChessBoardController = null;
        // 六边形单机模式棋盘控制器
        _this.hexSingleChessBoardController = null;
        // 测试按钮（用于调试显示地雷位置）
        _this.debugShowMinesButton = null;
        // 测试预制体（用于显示地雷位置）
        _this.debugMinePrefab = null;
        // 存储创建的测试预制体节点，用于清理
        _this.debugMineNodes = [];
        // 结算页面相关节点
        _this.levelSettlementNode = null; // level_settlement节点
        _this.boardBgNode = null; // level_settlement/board_bg节点
        _this.loseBgNode = null; // level_settlement/board_bg/lose_bg节点
        _this.winBgNode = null; // level_settlement/board_bg/win_bg节点
        _this.retryButton = null; // 再来一次按钮
        _this.nextLevelButton = null; // 下一关按钮
        _this.exitButton = null; // 退出按钮
        // 当前关卡数据
        _this.currentLevel = 1;
        _this.currentLevelInfo = null;
        _this.currentRoomId = 0; // 当前关卡游戏的房间ID
        _this.currentSingleChessBoard = null; // 当前激活的单机棋盘
        // 记录最后一次点击是否是炸弹
        _this.lastClickWasBomb = false;
        // 性能优化相关
        _this.lastShownMapNode = null; // 记录上次显示的地图节点
        _this.isUpdating = false; // 防止重复更新
        return _this;
    }
    LevelPageController.prototype.onLoad = function () {
        var _this = this;
        // 设置返回按钮点击事件 - 使用与GamePageController相同的样式
        if (this.backButton) {
            Tools_1.Tools.imageButtonClick(this.backButton, Config_1.Config.buttonRes + 'side_btn_back_normal', Config_1.Config.buttonRes + 'side_btn_back_pressed', function () {
                _this.onBackButtonClick();
            });
        }
        // 设置开始游戏按钮点击事件
        if (this.startGameButton) {
            this.startGameButton.node.on('click', this.onStartGameButtonClick, this);
        }
        // 注册单机模式消息监听
        this.registerSingleModeMessageHandlers();
        // 设置结算页面按钮事件
        this.setupSettlementButtons();
        // 注意：不在onLoad中初始化关卡数UI，等待外部调用setCurrentLevel时再更新
    };
    LevelPageController.prototype.start = function () {
        // 初始化时隐藏所有地图节点
        this.hideAllMapNodes();
        // 设置测试按钮点击事件
        this.setupDebugButton();
    };
    /**
     * 返回按钮点击事件
     */
    LevelPageController.prototype.onBackButtonClick = function () {
        // 如果没有有效的房间ID，说明还没有进入游戏，直接返回关卡选择页面
        if (this.currentRoomId <= 0) {
            this.returnToLevelSelect();
            return;
        }
        // 弹出确认退出对话框，type=1表示退出本局游戏，传递当前房间ID
        if (this.leaveDialogController) {
            this.leaveDialogController.show(1, function () {
            }, this.currentRoomId);
        }
        else {
            cc.warn("LeaveDialogController 未配置");
        }
    };
    /**
     * 返回到关卡选择页面
     */
    LevelPageController.prototype.returnToLevelSelect = function () {
        // 查找GlobalManagerController并切换到大厅页面
        var globalManagerNode = cc.find("Canvas/global_node") || cc.find("global_node");
        if (globalManagerNode) {
            var globalManager = globalManagerNode.getComponent(GlobalManagerController_1.default);
            if (globalManager) {
                globalManager.setCurrentPage(GlobalManagerController_1.PageType.HALL_PAGE);
            }
        }
    };
    /**
     * 禁用返回按钮
     */
    LevelPageController.prototype.disableBackButton = function () {
        if (this.backButton) {
            // 移除所有触摸事件监听器
            this.backButton.off(cc.Node.EventType.TOUCH_START);
            this.backButton.off(cc.Node.EventType.TOUCH_END);
            this.backButton.off(cc.Node.EventType.TOUCH_CANCEL);
            // 设置按钮为半透明状态，表示禁用
            this.backButton.opacity = 128;
        }
    };
    /**
     * 启用返回按钮
     */
    LevelPageController.prototype.enableBackButton = function () {
        var _this = this;
        if (this.backButton) {
            // 恢复按钮透明度
            this.backButton.opacity = 255;
            // 重新设置返回按钮点击事件
            Tools_1.Tools.imageButtonClick(this.backButton, Config_1.Config.buttonRes + 'side_btn_back_normal', Config_1.Config.buttonRes + 'side_btn_back_pressed', function () {
                _this.onBackButtonClick();
            });
        }
    };
    /**
     * 开始游戏按钮点击事件
     */
    LevelPageController.prototype.onStartGameButtonClick = function () {
        // ExtendLevelInfo消息现在由LevelSelectPageController发送
        // 这里直接进入游戏，等待后端响应
    };
    /**
     * 处理ExtendLevelInfo响应
     * @param levelInfo 关卡信息响应数据
     */
    LevelPageController.prototype.onExtendLevelInfo = function (levelInfo) {
        this.currentLevelInfo = levelInfo;
        // 保存房间ID，用于退出时使用
        if (levelInfo.roomId) {
            this.currentRoomId = levelInfo.roomId;
        }
        // 检查是否是断线重连
        // 1. 有mineMap数据且包含已挖掘的方块表示断线重连
        // 2. reconnected字段为true表示断线重连
        // 3. gameStatus为1且有游戏进度表示游戏进行中（可能是断线重连）
        var hasRevealedBlocks = levelInfo.mineMap &&
            levelInfo.mineMap.revealedBlocks &&
            Array.isArray(levelInfo.mineMap.revealedBlocks) &&
            levelInfo.mineMap.revealedBlocks.length > 0;
        var isReconnect = hasRevealedBlocks ||
            levelInfo.reconnected === true;
        if (isReconnect) {
            // 断线重连时，需要确定正确的关卡编号
            var serverLevelId = levelInfo.levelId;
            // 根据API文档，服务端的levelId可能从0开始，前端从1开始
            // 但是我们需要根据实际情况判断
            var targetLevel = this.currentLevel; // 默认使用当前设置的关卡
            // 如果服务端返回的levelId看起来是从0开始的（比当前关卡小1）
            if (serverLevelId === this.currentLevel - 1) {
                targetLevel = this.currentLevel;
            }
            // 如果服务端返回的levelId看起来是从1开始的（与当前关卡相同）
            else if (serverLevelId === this.currentLevel) {
                targetLevel = this.currentLevel;
            }
            // 其他情况，使用服务端返回的数据
            else {
                targetLevel = serverLevelId;
                this.setCurrentLevel(targetLevel);
            }
            // 断线重连时不重置关卡状态，保持当前状态
            // 重置炸弹点击标记
            this.lastClickWasBomb = false;
            // 更新地雷数UI
            this.updateMineCountUI(levelInfo.mineCount);
            // 使用当前设置的关卡编号进入关卡
            this.enterLevel(this.currentLevel);
            // 通知当前激活的单机棋盘控制器处理断线重连
            if (this.currentSingleChessBoard) {
                this.currentSingleChessBoard.onExtendLevelInfoReconnect(levelInfo);
            }
        }
        else {
            // 检查服务端返回的关卡ID是否与当前设置一致
            var serverLevelId = levelInfo.levelId;
            // 重置关卡状态（包括清除测试预制体）
            this.resetLevelState();
            // 重置炸弹点击标记
            this.lastClickWasBomb = false;
            // 开始新游戏时，先重置当前棋盘（清理上一局的痕迹）
            if (this.currentSingleChessBoard) {
                this.currentSingleChessBoard.resetBoard();
            }
            // 更新地雷数UI
            this.updateMineCountUI(levelInfo.mineCount);
            // 使用当前设置的关卡编号，而不是后端返回的levelId
            // 因为后端的levelId可能与前端的关卡编号不一致
            this.enterLevel(this.currentLevel);
            // 通知当前激活的单机棋盘控制器处理ExtendLevelInfo
            if (this.currentSingleChessBoard) {
                this.currentSingleChessBoard.onExtendLevelInfo();
            }
        }
    };
    /**
     * 更新地雷数UI
     * @param mineCount 地雷数量
     */
    LevelPageController.prototype.updateMineCountUI = function (mineCount) {
        if (this.mineCountLabel) {
            this.mineCountLabel.string = mineCount.toString();
        }
    };
    /**
     * 更新当前关卡数UI
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.updateCurrentLevelUI = function (levelNumber) {
        if (this.currentLevelLabel) {
            this.currentLevelLabel.string = "\u7B2C" + levelNumber + "\u5173";
        }
    };
    /**
     * 根据关卡数进入相应的关卡（优化版本）
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.enterLevel = function (levelNumber) {
        // 防止重复更新
        if (this.isUpdating) {
            return;
        }
        this.isUpdating = true;
        // 更新关卡数UI显示
        this.updateCurrentLevelUI(levelNumber);
        // 获取目标地图节点和容器
        var targetMapInfo = this.getMapNodeByLevel(levelNumber);
        if (!targetMapInfo) {
            cc.warn("\u672A\u77E5\u7684\u5173\u5361\u7F16\u53F7: " + levelNumber);
            this.isUpdating = false;
            return;
        }
        // 只有当目标节点与当前显示的节点不同时才进行切换
        if (this.lastShownMapNode !== targetMapInfo.mapNode) {
            // 隐藏上一个显示的地图节点
            if (this.lastShownMapNode) {
                this.lastShownMapNode.active = false;
            }
            // 显示目标容器和地图节点
            this.showMapContainer(targetMapInfo.containerType);
            this.showMapNodeOptimized(targetMapInfo.mapNode, targetMapInfo.mapName);
            // 记录当前显示的节点
            this.lastShownMapNode = targetMapInfo.mapNode;
        }
        // 设置当前激活的单机棋盘控制器
        if (this.isHexLevel(levelNumber)) {
            // 六边形关卡使用六边形控制器
            if (this.hexSingleChessBoardController) {
                var hexBoardType = this.getHexBoardTypeByLevel(levelNumber);
                this.hexSingleChessBoardController.initBoard(hexBoardType);
                this.currentSingleChessBoard = this.hexSingleChessBoardController;
            }
            else {
                console.error("❌ 六边形单机控制器未配置！");
                this.currentSingleChessBoard = null;
            }
        }
        else {
            // 四边形关卡使用四边形控制器
            if (this.singleChessBoardController) {
                var boardType = this.getBoardTypeByLevel(levelNumber);
                this.singleChessBoardController.initBoard(boardType);
                this.currentSingleChessBoard = this.singleChessBoardController;
            }
            else {
                console.error("❌ 四边形单机控制器未配置！");
                this.currentSingleChessBoard = null;
            }
        }
        this.isUpdating = false;
    };
    /**
     * 根据关卡数获取对应的地图节点信息
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.getMapNodeByLevel = function (levelNumber) {
        if (levelNumber >= 1 && levelNumber <= 4) {
            return { mapNode: this.qipan8x8Node, mapName: "qipan8*8", containerType: 'map1' };
        }
        else if (levelNumber === 5) {
            return { mapNode: this.levelS001Node, mapName: "Level_S001", containerType: 'map2' };
        }
        else if (levelNumber >= 6 && levelNumber <= 9) {
            return { mapNode: this.qipan8x9Node, mapName: "qipan8*9", containerType: 'map1' };
        }
        else if (levelNumber === 10) {
            return { mapNode: this.levelS002Node, mapName: "Level_S002", containerType: 'map2' };
        }
        else if (levelNumber >= 11 && levelNumber <= 14) {
            return { mapNode: this.qipan9x9Node, mapName: "qipan9*9", containerType: 'map1' };
        }
        else if (levelNumber === 15) {
            return { mapNode: this.levelS003Node, mapName: "Level_S003", containerType: 'map2' };
        }
        else if (levelNumber >= 16 && levelNumber <= 19) {
            return { mapNode: this.qipan9x10Node, mapName: "qipan9*10", containerType: 'map1' };
        }
        else if (levelNumber === 20) {
            return { mapNode: this.levelS004Node, mapName: "Level_S004", containerType: 'map2' };
        }
        else if (levelNumber >= 21 && levelNumber <= 24) {
            return { mapNode: this.qipan10x10Node, mapName: "qipan10*10", containerType: 'map1' };
        }
        else if (levelNumber === 25) {
            return { mapNode: this.levelS005Node, mapName: "Level_S005", containerType: 'map2' };
        }
        else if (levelNumber >= 26 && levelNumber <= 29) {
            return { mapNode: this.qipan10x10Node, mapName: "qipan10*10", containerType: 'map1' };
        }
        else if (levelNumber === 30) {
            return { mapNode: this.levelS006Node, mapName: "Level_S006", containerType: 'map2' };
        }
        return null;
    };
    /**
     * 根据关卡编号获取棋盘类型
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.getBoardTypeByLevel = function (levelNumber) {
        if (levelNumber >= 1 && levelNumber <= 4) {
            return "8x8";
        }
        else if (levelNumber >= 6 && levelNumber <= 9) {
            return "8x9";
        }
        else if (levelNumber >= 11 && levelNumber <= 14) {
            return "9x9";
        }
        else if (levelNumber >= 16 && levelNumber <= 19) {
            return "9x10";
        }
        else if (levelNumber >= 21 && levelNumber <= 24 || levelNumber >= 26 && levelNumber <= 29) {
            return "10x10";
        }
        return "8x8"; // 默认
    };
    /**
     * 判断是否为六边形关卡
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.isHexLevel = function (levelNumber) {
        // 特殊关卡使用六边形棋盘
        return levelNumber === 5 || levelNumber === 10 || levelNumber === 15 ||
            levelNumber === 20 || levelNumber === 25 || levelNumber === 30;
    };
    /**
     * 根据关卡编号获取六边形棋盘类型
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.getHexBoardTypeByLevel = function (levelNumber) {
        // 六关对应六个六边形棋盘
        if (levelNumber === 5) {
            return "hexBoard1"; // 第1关六边形
        }
        else if (levelNumber === 10) {
            return "hexBoard2"; // 第2关六边形
        }
        else if (levelNumber === 15) {
            return "hexBoard3"; // 第3关六边形
        }
        else if (levelNumber === 20) {
            return "hexBoard4"; // 第4关六边形
        }
        else if (levelNumber === 25) {
            return "hexBoard5"; // 第5关六边形
        }
        else if (levelNumber === 30) {
            return "hexBoard6"; // 第6关六边形
        }
        return "hexBoard1"; // 默认
    };
    /**
     * 显示指定的地图节点（优化版本）
     * @param mapNode 要显示的地图节点
     * @param mapName 地图名称（用于日志）
     */
    LevelPageController.prototype.showMapNodeOptimized = function (mapNode, mapName) {
        if (mapNode) {
            mapNode.active = true;
        }
        else {
            cc.warn("\u274C \u5730\u56FE\u8282\u70B9\u672A\u627E\u5230: " + mapName);
        }
    };
    /**
     * 显示指定的地图容器
     * @param containerType 容器类型
     */
    LevelPageController.prototype.showMapContainer = function (containerType) {
        // 确保 level_page 节点是激活的
        if (this.levelPageNode) {
            this.levelPageNode.active = true;
        }
        // 根据容器类型显示对应容器，隐藏另一个
        if (containerType === 'map1') {
            if (this.gameMap1Node && !this.gameMap1Node.active) {
                this.gameMap1Node.active = true;
            }
            if (this.gameMap2Node && this.gameMap2Node.active) {
                this.gameMap2Node.active = false;
            }
        }
        else {
            if (this.gameMap2Node && !this.gameMap2Node.active) {
                this.gameMap2Node.active = true;
            }
            if (this.gameMap1Node && this.gameMap1Node.active) {
                this.gameMap1Node.active = false;
            }
        }
    };
    /**
     * 隐藏所有地图节点
     */
    LevelPageController.prototype.hideAllMapNodes = function () {
        var allMapNodes = [
            this.qipan8x8Node,
            this.qipan8x9Node,
            this.qipan9x9Node,
            this.qipan9x10Node,
            this.qipan10x10Node,
            this.levelS001Node,
            this.levelS002Node,
            this.levelS003Node,
            this.levelS004Node,
            this.levelS005Node,
            this.levelS006Node
        ];
        allMapNodes.forEach(function (node) {
            if (node) {
                node.active = false;
            }
        });
    };
    /**
     * 设置当前关卡（从外部调用）
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.setCurrentLevel = function (levelNumber) {
        // 重置关卡状态（包括清除测试预制体）
        this.resetLevelState();
        this.currentLevel = levelNumber;
        // 立即根据关卡数切换地图显示
        this.enterLevel(levelNumber);
    };
    /**
     * 获取当前关卡编号
     */
    LevelPageController.prototype.getCurrentLevel = function () {
        return this.currentLevel;
    };
    /**
     * 获取当前关卡信息
     */
    LevelPageController.prototype.getCurrentLevelInfo = function () {
        return this.currentLevelInfo;
    };
    /**
     * 获取当前房间ID
     */
    LevelPageController.prototype.getCurrentRoomId = function () {
        return this.currentRoomId;
    };
    /**
     * 隐藏所有地图容器
     */
    LevelPageController.prototype.hideAllMapContainers = function () {
        // 确保 level_page 节点是激活的
        if (this.levelPageNode) {
            this.levelPageNode.active = true;
        }
        // 隐藏两个主要的地图容器
        if (this.gameMap1Node) {
            this.gameMap1Node.active = false;
        }
        if (this.gameMap2Node) {
            this.gameMap2Node.active = false;
        }
        // 同时隐藏所有具体的地图节点
        this.hideAllMapNodes();
    };
    /**
     * 显示 game_map_1 容器（方形地图）
     */
    LevelPageController.prototype.showGameMap1 = function () {
        if (this.gameMap1Node) {
            this.gameMap1Node.active = true;
        }
        else {
            cc.warn("❌ game_map_1 节点未找到");
        }
    };
    /**
     * 显示 game_map_2 容器（特殊关卡）
     */
    LevelPageController.prototype.showGameMap2 = function () {
        if (this.gameMap2Node) {
            this.gameMap2Node.active = true;
        }
        else {
            cc.warn("❌ game_map_2 节点未找到");
        }
    };
    /**
     * 获取当前激活的单机棋盘控制器
     */
    LevelPageController.prototype.getCurrentSingleChessBoard = function () {
        return this.currentSingleChessBoard;
    };
    /**
     * 处理单机模式的点击响应
     * @param response 点击响应数据
     */
    LevelPageController.prototype.handleSingleModeClickResponse = function (response) {
        if (this.currentSingleChessBoard) {
            var x = response.x, y = response.y, result = response.result, chainReaction = response.chainReaction;
            // 处理点击结果
            if (x !== undefined && y !== undefined && result !== undefined) {
                this.currentSingleChessBoard.handleClickResponse(x, y, result);
            }
            // 处理连锁反应
            if (chainReaction && Array.isArray(chainReaction)) {
                this.currentSingleChessBoard.handleChainReaction(chainReaction);
            }
        }
    };
    /**
     * 处理单机模式游戏结束
     * @param gameEndData 游戏结束数据
     */
    LevelPageController.prototype.handleSingleModeGameEnd = function (gameEndData) {
        if (this.currentSingleChessBoard) {
            // 禁用棋盘触摸
            this.currentSingleChessBoard.disableAllGridTouch();
            // 处理游戏结束
            this.currentSingleChessBoard.onLevelGameEnd();
        }
    };
    /**
     * 重置当前单机棋盘（仅在开始新游戏时调用）
     */
    LevelPageController.prototype.resetCurrentSingleChessBoard = function () {
        if (this.currentSingleChessBoard) {
            // 重置棋盘状态（清理所有预制体和格子状态）
            this.currentSingleChessBoard.resetBoard();
            // 重新启用触摸事件
            this.currentSingleChessBoard.enableAllGridTouch();
        }
    };
    /**
     * 注册单机模式消息处理器
     */
    LevelPageController.prototype.registerSingleModeMessageHandlers = function () {
        // 监听WebSocket消息
        GameMgr_1.GameMgr.Event.AddEventListener(EventCenter_1.EventType.ReceiveMessage, this.onReceiveMessage, this);
    };
    /**
     * 取消单机模式消息监听
     */
    LevelPageController.prototype.unregisterSingleModeMessageHandlers = function () {
        GameMgr_1.GameMgr.Event.RemoveEventListener(EventCenter_1.EventType.ReceiveMessage, this.onReceiveMessage, this);
    };
    /**
     * 处理接收到的WebSocket消息
     * @param messageBean 消息数据
     */
    LevelPageController.prototype.onReceiveMessage = function (messageBean) {
        switch (messageBean.msgId) {
            case MessageId_1.MessageId.MsgTypeLevelClickBlock:
                this.onLevelClickBlockResponse(messageBean.data);
                break;
            case MessageId_1.MessageId.MsgTypeLevelGameEnd:
                this.onLevelGameEnd(messageBean.data);
                break;
        }
    };
    /**
     * 处理LevelClickBlock响应
     * @param response 点击响应数据
     */
    LevelPageController.prototype.onLevelClickBlockResponse = function (response) {
        if (this.currentSingleChessBoard) {
            // 解构响应数据，支持多种可能的字段名
            var x = response.x, y = response.y, q = response.q, r = response.r, result = response.result, action = response.action, chainReaction = response.chainReaction, revealedGrids = response.revealedGrids, floodFill = response.floodFill, revealedBlocks = response.revealedBlocks, floodFillResults = response.floodFillResults // 单机模式使用这个字段
            ;
            // 判断当前使用的是哪种控制器
            var isUsingHexController = (this.currentSingleChessBoard === this.hexSingleChessBoardController);
            // 根据控制器类型决定坐标处理方式
            var coordX = void 0, coordY = void 0, coordQ = void 0, coordR = void 0;
            var hasValidCoord = false;
            if (isUsingHexController) {
                // 六边形控制器：优先使用六边形坐标，如果没有则将x,y映射为q,r
                if (q !== undefined && r !== undefined) {
                    coordQ = q;
                    coordR = r;
                    hasValidCoord = true;
                }
                else if (x !== undefined && y !== undefined) {
                    // 服务器返回x,y字段，但实际是六边形坐标：x=q, y=r
                    coordQ = x; // x 就是 q
                    coordR = y; // y 就是 r
                    hasValidCoord = true;
                }
            }
            else {
                // 四边形控制器：使用四边形坐标
                if (x !== undefined && y !== undefined) {
                    coordX = x;
                    coordY = y;
                    hasValidCoord = true;
                }
                else if (q !== undefined && r !== undefined) {
                    console.warn("\u26A0\uFE0F \u56DB\u8FB9\u5F62\u63A7\u5236\u5668\u6536\u5230\u516D\u8FB9\u5F62\u5750\u6807 (" + q + ", " + r + ")\uFF0C\u8FD9\u53EF\u80FD\u4E0D\u6B63\u786E");
                    hasValidCoord = false;
                }
            }
            if (hasValidCoord && result !== undefined) {
                if (action === 2) {
                    // 标记/取消标记操作，不调用handleClickResponse，避免格子消失
                    // 不调用 handleClickResponse，因为标记操作不应该隐藏格子
                }
                else if (action === 1) {
                    // 挖掘操作
                    // 检查是否点到炸弹
                    if (result === "boom" || result === "mine") {
                        this.lastClickWasBomb = true;
                    }
                    // 根据控制器类型调用对应的方法
                    if (isUsingHexController) {
                        // 六边形控制器
                        this.currentSingleChessBoard.handleClickResponse(coordQ, coordR, result);
                    }
                    else {
                        // 四边形控制器
                        this.currentSingleChessBoard.handleClickResponse(coordX, coordY, result);
                    }
                    // 处理连锁展开数据
                    if (floodFillResults && Array.isArray(floodFillResults) && floodFillResults.length > 0) {
                        if (isUsingHexController) {
                            // 六边形控制器使用handleChainReaction方法
                            this.currentSingleChessBoard.handleChainReaction(floodFillResults);
                        }
                        else {
                            // 四边形控制器使用handleFloodFillResults方法
                            this.currentSingleChessBoard.handleFloodFillResults(floodFillResults);
                        }
                    }
                }
                else {
                    // 其他操作，默认按挖掘处理
                    if (isUsingHexController) {
                        this.currentSingleChessBoard.handleClickResponse(coordQ, coordR, result);
                    }
                    else {
                        this.currentSingleChessBoard.handleClickResponse(coordX, coordY, result);
                    }
                }
            }
            else {
                console.warn("⚠️ 响应数据缺少有效坐标或结果信息");
                console.warn("   \u63A7\u5236\u5668\u7C7B\u578B: " + (isUsingHexController ? '六边形' : '四边形'));
                console.warn("   \u5750\u6807\u6570\u636E: x=" + x + ", y=" + y + ", q=" + q + ", r=" + r);
                console.warn("   \u7ED3\u679C: " + result);
            }
        }
    };
    /**
     * 处理LevelGameEnd通知
     * @param gameEndData 游戏结束数据
     */
    LevelPageController.prototype.onLevelGameEnd = function (gameEndData) {
        var _this = this;
        if (this.currentSingleChessBoard) {
            // 禁用棋盘触摸
            this.currentSingleChessBoard.disableAllGridTouch();
            // 处理游戏结束（不清理数据）
            this.currentSingleChessBoard.onLevelGameEnd();
        }
        // 禁用返回按钮，防止游戏结束时玩家误点击
        this.disableBackButton();
        // 检查是否点到了炸弹，如果是游戏失败且点到炸弹，则延迟显示结算页面
        var isGameFailed = !gameEndData.success;
        var hasBombExploded = this.lastClickWasBomb ||
            (this.currentSingleChessBoard && this.currentSingleChessBoard.hasBombExplodedInThisGame());
        if (isGameFailed && hasBombExploded) {
            // 点到炸弹导致的游戏失败，延迟1.5秒显示结算页面
            this.scheduleOnce(function () {
                _this.showLevelSettlement(gameEndData);
                // 重置标记
                _this.lastClickWasBomb = false;
            }, 1.5);
        }
        else {
            // 其他情况，立即显示结算页面
            this.showLevelSettlement(gameEndData);
            // 重置标记
            this.lastClickWasBomb = false;
        }
    };
    /**
     * 设置结算页面按钮事件
     */
    LevelPageController.prototype.setupSettlementButtons = function () {
        var _this = this;
        // 再来一次按钮
        if (this.retryButton) {
            this.retryButton.node.on('click', this.onRetryButtonClick, this);
        }
        // 下一关按钮
        if (this.nextLevelButton) {
            this.nextLevelButton.node.on('click', this.onNextLevelButtonClick, this);
        }
        // 退出按钮 - 使用按压效果，模仿其他返回按钮
        if (this.exitButton) {
            Tools_1.Tools.imageButtonClick(this.exitButton.node, Config_1.Config.buttonRes + 'board_btn_back_normal', Config_1.Config.buttonRes + 'board_btn_back_pressed', function () {
                _this.onExitButtonClick();
            });
        }
    };
    /**
     * 显示结算页面
     * @param gameEndData 游戏结束数据
     */
    LevelPageController.prototype.showLevelSettlement = function (gameEndData) {
        if (!this.levelSettlementNode) {
            console.error("levelSettlementNode 未配置");
            return;
        }
        // 显示结算页面
        this.levelSettlementNode.active = true;
        // 根据游戏结果显示对应的背景 - 修复成功判断逻辑
        var isSuccess = gameEndData.isSuccess || gameEndData.success || gameEndData.isWin || gameEndData.gameStatus === 1;
        if (isSuccess) {
            // 成功 - 显示胜利背景
            if (this.winBgNode) {
                this.winBgNode.active = true;
            }
            if (this.loseBgNode) {
                this.loseBgNode.active = false;
            }
        }
        else {
            // 失败 - 显示失败背景
            if (this.loseBgNode) {
                this.loseBgNode.active = true;
            }
            if (this.winBgNode) {
                this.winBgNode.active = false;
            }
        }
    };
    /**
     * 再来一次按钮点击事件
     */
    LevelPageController.prototype.onRetryButtonClick = function () {
        // 关闭结算页面
        this.hideLevelSettlement();
        // 重新启用返回按钮
        this.enableBackButton();
        // 注意：不在这里重置棋盘，等收到ExtendLevelInfo响应时再重置
        // 这样可以避免过早清理，让玩家在点击按钮后还能短暂看到游玩痕迹
        // 发送当前关卡的ExtendLevelInfo
        this.sendExtendLevelInfo(this.currentLevel);
    };
    /**
     * 下一关按钮点击事件
     */
    LevelPageController.prototype.onNextLevelButtonClick = function () {
        // 关闭结算页面
        this.hideLevelSettlement();
        // 重新启用返回按钮
        this.enableBackButton();
        // 进入下一关
        var nextLevel = this.currentLevel + 1;
        this.setCurrentLevel(nextLevel);
        // 注意：不在这里重置棋盘，等收到ExtendLevelInfo响应时再重置
        // 这样可以避免过早清理，让玩家在点击按钮后还能短暂看到游玩痕迹
        // 发送下一关的ExtendLevelInfo
        this.sendExtendLevelInfo(nextLevel);
    };
    /**
     * 退出按钮点击事件
     */
    LevelPageController.prototype.onExitButtonClick = function () {
        // 关闭结算页面
        this.hideLevelSettlement();
        // 重新启用返回按钮（虽然要退出了，但保持一致性）
        this.enableBackButton();
        // 返回到关卡选择页面（匹配界面）
        this.returnToLevelSelect();
    };
    /**
     * 隐藏结算页面
     */
    LevelPageController.prototype.hideLevelSettlement = function () {
        if (this.levelSettlementNode) {
            this.levelSettlementNode.active = false;
        }
    };
    /**
     * 发送ExtendLevelInfo消息
     * @param levelId 关卡ID
     */
    LevelPageController.prototype.sendExtendLevelInfo = function (levelId) {
        var request = {
            levelId: levelId
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeExtendLevelInfo, request);
    };
    /**
     * 设置测试按钮
     */
    LevelPageController.prototype.setupDebugButton = function () {
        if (this.debugShowMinesButton) {
            this.debugShowMinesButton.node.on('click', this.onDebugShowMinesClick, this);
        }
    };
    /**
     * 测试按钮点击事件 - 发送DebugShowMines消息
     */
    LevelPageController.prototype.onDebugShowMinesClick = function () {
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeDebugShowMines, {});
    };
    /**
     * 判断是否在单机模式
     */
    LevelPageController.prototype.isInSingleMode = function () {
        // 单机模式的判断逻辑：当前页面是关卡页面且有有效的房间ID
        return this.currentRoomId > 0;
    };
    /**
     * 处理DebugShowMines响应，在炸弹位置生成测试预制体
     * @param minePositions 炸弹位置数组 [{x: number, y: number}]
     */
    LevelPageController.prototype.handleDebugShowMines = function (minePositions) {
        var _this = this;
        if (!this.debugMinePrefab) {
            cc.warn("debugMinePrefab 预制体未设置，无法显示测试标记");
            return;
        }
        if (!this.currentSingleChessBoard) {
            cc.warn("当前没有激活的单机棋盘");
            return;
        }
        if (!minePositions || !Array.isArray(minePositions) || minePositions.length === 0) {
            cc.warn("地雷位置数据无效:", minePositions);
            return;
        }
        // 先尝试直接创建一个测试预制体，不使用延迟
        if (minePositions.length > 0) {
            var firstPosition = minePositions[0];
            // 检查坐标字段
            var coordX = void 0, coordY = void 0;
            var pos = firstPosition; // 使用any类型避免TypeScript报错
            if (pos.x !== undefined && pos.y !== undefined) {
                coordX = pos.x;
                coordY = pos.y;
            }
            else if (pos.q !== undefined && pos.r !== undefined) {
                coordX = pos.q;
                coordY = pos.r;
            }
            else {
                console.error("\u274C \u65E0\u6CD5\u8BC6\u522B\u5750\u6807\u5B57\u6BB5:", firstPosition);
                return;
            }
            // 直接调用，不使用延迟
            this.createDebugMinePrefab(coordX, coordY);
        }
        // 在每个炸弹位置生成测试预制体
        minePositions.forEach(function (position, index) {
            var pos = position; // 使用any类型避免TypeScript报错
            // 获取坐标
            var coordX, coordY;
            if (pos.x !== undefined && pos.y !== undefined) {
                coordX = pos.x;
                coordY = pos.y;
            }
            else if (pos.q !== undefined && pos.r !== undefined) {
                coordX = pos.q;
                coordY = pos.r;
            }
            else {
                console.error("\u274C \u5730\u96F7\u4F4D\u7F6E" + index + "\u5750\u6807\u5B57\u6BB5\u65E0\u6548:", position);
                return;
            }
            if (index === 0) {
                // 第一个不延迟，立即执行（已经在上面处理过了，跳过）
                return;
            }
            else {
                // 其他的使用延迟 - 修复闭包问题
                var capturedX_1 = coordX; // 捕获当前值
                var capturedY_1 = coordY; // 捕获当前值
                _this.scheduleOnce(function () {
                    _this.createDebugMinePrefab(capturedX_1, capturedY_1);
                }, index * 0.1);
            }
        });
    };
    /**
     * 在指定位置创建测试预制体
     * @param x 格子x坐标（四边形）或q坐标（六边形）
     * @param y 格子y坐标（四边形）或r坐标（六边形）
     */
    LevelPageController.prototype.createDebugMinePrefab = function (x, y) {
        // 检查坐标是否有效
        if (x === undefined || y === undefined || x === null || y === null) {
            console.error("\u274C \u65E0\u6548\u7684\u5750\u6807\u53C2\u6570: x=" + x + ", y=" + y);
            console.error("   x\u7C7B\u578B: " + typeof x + ", y\u7C7B\u578B: " + typeof y);
            console.error("   \u8C03\u7528\u5806\u6808:", new Error().stack);
            return;
        }
        if (!this.debugMinePrefab) {
            cc.error("debugMinePrefab 为空，无法创建测试预制体");
            return;
        }
        if (!this.currentSingleChessBoard) {
            cc.error("currentSingleChessBoard 为空，无法创建测试预制体");
            return;
        }
        try {
            // 判断当前使用的是哪种控制器
            var isUsingHexController = (this.currentSingleChessBoard === this.hexSingleChessBoardController);
            var debugNode = null;
            if (isUsingHexController) {
                // 六边形控制器：x实际是q，y实际是r
                debugNode = this.currentSingleChessBoard.createCustomPrefab(x, y, // 对于六边形，x就是q，y就是r
                this.debugMinePrefab, "DebugMine_" + x + "_" + y);
            }
            else {
                // 四边形控制器
                debugNode = this.currentSingleChessBoard.createCustomPrefab(x, y, this.debugMinePrefab, "DebugMine_" + x + "_" + y);
            }
            if (debugNode) {
                // 将创建的节点存储起来，用于后续清理
                this.debugMineNodes.push(debugNode);
            }
            else {
                cc.error("\u274C \u5728\u4F4D\u7F6E (" + x + ", " + y + ") \u521B\u5EFA\u6D4B\u8BD5\u9884\u5236\u4F53\u5931\u8D25\uFF0C\u8FD4\u56DE\u503C\u4E3A\u7A7A");
            }
        }
        catch (error) {
            cc.error("\u274C \u521B\u5EFA\u6D4B\u8BD5\u9884\u5236\u4F53\u65F6\u53D1\u751F\u9519\u8BEF:", error);
        }
    };
    /**
     * 清除所有测试预制体
     */
    LevelPageController.prototype.clearDebugMines = function () {
        this.debugMineNodes.forEach(function (node, index) {
            if (node && cc.isValid(node)) {
                node.destroy();
            }
        });
        // 清空数组
        this.debugMineNodes = [];
    };
    /**
     * 重置关卡状态（包括清除测试预制体）
     */
    LevelPageController.prototype.resetLevelState = function () {
        this.clearDebugMines();
        // 这里可以添加其他需要重置的状态
    };
    LevelPageController.prototype.onDestroy = function () {
        // 取消消息监听
        this.unregisterSingleModeMessageHandlers();
        // 清理测试预制体
        this.clearDebugMines();
        // 清理按钮事件
        if (this.retryButton) {
            this.retryButton.node.off('click', this.onRetryButtonClick, this);
        }
        if (this.nextLevelButton) {
            this.nextLevelButton.node.off('click', this.onNextLevelButtonClick, this);
        }
        // 退出按钮使用 Tools.imageButtonClick，会自动管理事件，无需手动清理
        if (this.debugShowMinesButton) {
            this.debugShowMinesButton.node.off('click', this.onDebugShowMinesClick, this);
        }
    };
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "backButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "startGameButton", void 0);
    __decorate([
        property(cc.Label)
    ], LevelPageController.prototype, "mineCountLabel", void 0);
    __decorate([
        property(cc.Label)
    ], LevelPageController.prototype, "currentLevelLabel", void 0);
    __decorate([
        property(LeaveDialogController_1.default)
    ], LevelPageController.prototype, "leaveDialogController", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelPageNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "gameMap1Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "gameMap2Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan8x8Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan8x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan9x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan9x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan10x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS001Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS002Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS003Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS004Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS005Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS006Node", void 0);
    __decorate([
        property(SingleChessBoardController_1.default)
    ], LevelPageController.prototype, "singleChessBoardController", void 0);
    __decorate([
        property(HexSingleChessBoardController_1.default)
    ], LevelPageController.prototype, "hexSingleChessBoardController", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "debugShowMinesButton", void 0);
    __decorate([
        property(cc.Prefab)
    ], LevelPageController.prototype, "debugMinePrefab", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelSettlementNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "boardBgNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "loseBgNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "winBgNode", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "retryButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "nextLevelButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "exitButton", void 0);
    LevelPageController = __decorate([
        ccclass
    ], LevelPageController);
    return LevelPageController;
}(cc.Component));
exports.default = LevelPageController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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