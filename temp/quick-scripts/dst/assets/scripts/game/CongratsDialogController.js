
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/CongratsDialogController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'f3b5eIOZ4tF8YT//S1WoI5Z', 'CongratsDialogController');
// scripts/game/CongratsDialogController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../bean/GlobalBean");
var EventCenter_1 = require("../common/EventCenter");
var GameMgr_1 = require("../common/GameMgr");
var MessageBaseBean_1 = require("../net/MessageBaseBean");
var CongratsItemController_1 = require("../pfb/CongratsItemController");
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
//结算页面
var CongratsDialogController = /** @class */ (function (_super) {
    __extends(CongratsDialogController, _super);
    function CongratsDialogController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBg = null;
        _this.contentLay = null;
        _this.backBtn = null;
        _this.congratsItem = null; //列表的 item
        _this.layoutNode = null; //存放列表的布局
        _this.countdownTimeLabel = null;
        _this.countdownInterval = null; //倒计时的 id
        _this.backCallback = null; //隐藏弹窗的回调
        _this.seconds = 10; //倒计时 10 秒
        return _this;
    }
    CongratsDialogController.prototype.onLoad = function () {
        this.countdownTimeLabel = this.backBtn.getChildByName('buttonLabel_time').getComponent(cc.Label);
    };
    CongratsDialogController.prototype.onEnable = function () {
        this.updateCountdownLabel(this.seconds);
        // Tools.setCountDownTimeLabel(this.backBtn)
    };
    CongratsDialogController.prototype.start = function () {
        var _this = this;
        //backBtn 按钮点击事件
        Tools_1.Tools.greenButton(this.backBtn, function () {
            _this.hide(true);
        });
        // 设置倒计时标签的固定位置
        this.setFixedCountdownPosition();
        // 设置整个按钮的点击效果（包括按钮背景和倒计时标签的统一反馈）
        this.setupButtonWithCountdownEffect();
    };
    CongratsDialogController.prototype.show = function (noticeSettlement, backCallback) {
        this.backCallback = backCallback;
        this.node.active = true;
        this.boardBg.scale = 0;
        // 每次显示时重新设置倒计时标签的固定位置，确保位置正确
        this.setFixedCountdownPosition();
        this._setData(noticeSettlement);
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 1 })
            .start();
    };
    CongratsDialogController.prototype._setData = function (noticeSettlement) {
        // 获取用户列表，优先使用 finalRanking，其次使用 users
        var userList = noticeSettlement.finalRanking || noticeSettlement.users;
        // 检查用户列表是否存在
        if (!noticeSettlement || !userList || !Array.isArray(userList)) {
            console.warn('NoticeSettlement 用户数据无效:', noticeSettlement);
            this.startCountdown(10); // 仍然启动倒计时
            return;
        }
        var currentUserId = GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId;
        var index = userList.findIndex(function (item) { return item.userId === currentUserId; }); //搜索
        if (index >= 0) {
            // 对于扫雷游戏，finalRanking 中有 coinChg 字段，需要更新金币
            if ('coin' in userList[index]) {
                // UserSettlement 类型，直接使用 coin 字段
                GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.coin = userList[index].coin;
            }
            else if ('coinChg' in userList[index]) {
                // PlayerFinalResult 类型，使用 coinChg 字段更新金币
                GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.coin += userList[index].coinChg;
            }
        }
        this.layoutNode.removeAllChildren();
        var _loop_1 = function (i) {
            var item = cc.instantiate(this_1.congratsItem);
            var data = userList[i];
            this_1.layoutNode.addChild(item);
            setTimeout(function () {
                item.getComponent(CongratsItemController_1.default).createData(data, GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users);
            }, 100);
        };
        var this_1 = this;
        for (var i = 0; i < userList.length; ++i) {
            _loop_1(i);
        }
        this.startCountdown(10); //倒计时 10 秒
    };
    // bool 在隐藏的时候是否返回大厅
    CongratsDialogController.prototype.hide = function (bool) {
        var _this = this;
        if (bool === void 0) { bool = false; }
        if (this.backCallback) {
            this.backCallback();
        }
        GameMgr_1.GameMgr.Console.Log('隐藏结算页面');
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 0 })
            .call(function () {
            _this.node.active = false;
            if (bool) {
                GlobalBean_1.GlobalBean.GetInstance().cleanData();
                var autoMessageBean = {
                    'msgId': MessageBaseBean_1.AutoMessageId.JumpHallPage,
                    'data': { 'type': 2 } //2是结算弹窗跳转的
                };
                GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.AutoMessage, autoMessageBean);
            }
        })
            .start();
    };
    CongratsDialogController.prototype.onDisable = function () {
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
        }
    };
    CongratsDialogController.prototype.startCountdown = function (seconds) {
        var _this = this;
        // 在开始倒计时前再次确保位置正确
        this.setFixedCountdownPosition();
        var remainingSeconds = seconds;
        this.updateCountdownLabel(remainingSeconds);
        this.countdownInterval = setInterval(function () {
            remainingSeconds--;
            if (remainingSeconds <= 0) {
                clearInterval(_this.countdownInterval);
                _this.countdownInterval = null;
                // 倒计时结束时的处理逻辑
                _this.hide(true);
                return;
            }
            _this.updateCountdownLabel(remainingSeconds);
        }, 1000);
    };
    CongratsDialogController.prototype.updateCountdownLabel = function (seconds) {
        if (this.countdownTimeLabel) {
            this.countdownTimeLabel.string = "\uFF08" + seconds + "s\uFF09";
        }
    };
    // 设置固定的倒计时位置，左括号始终对准back文字的右边
    CongratsDialogController.prototype.setFixedCountdownPosition = function () {
        if (!this.backBtn) {
            console.warn("CongratsDialogController: setFixedCountdownPosition - backBtn不存在");
            return;
        }
        // 重新获取节点，确保引用是最新的
        var btn = this.backBtn.getChildByName('button_label');
        var timeBtn = this.backBtn.getChildByName('buttonLabel_time');
        if (!btn || !timeBtn) {
            console.warn("CongratsDialogController: setFixedCountdownPosition - 缺少子节点", {
                hasBtn: !!btn,
                hasTimeBtn: !!timeBtn
            });
            return;
        }
        // 重新获取倒计时标签组件，确保引用正确
        this.countdownTimeLabel = timeBtn.getComponent(cc.Label);
        if (!this.countdownTimeLabel) {
            console.warn("CongratsDialogController: setFixedCountdownPosition - 无法获取Label组件");
            return;
        }
        // 延迟一帧执行，确保所有UI初始化完成
        this.scheduleOnce(function () {
            // 强制设置锚点为左对齐，确保左括号位置固定
            timeBtn.anchorX = 0;
            timeBtn.anchorY = 0.5; // 确保垂直居中
            // 计算固定位置：back文字右边缘 + 小间距，左括号固定在此位置
            var fixedLeftPos = btn.position.x + btn.width / 2 + 5; // 5像素间距
            // 直接设置位置
            timeBtn.setPosition(fixedLeftPos, 0);
            // 再次强制设置锚点，防止被其他代码重置
            timeBtn.anchorX = 0;
            timeBtn.anchorY = 0.5;
        }, 0.01);
    };
    // 设置整个按钮的统一点击效果（按钮背景和倒计时标签都有反馈）
    CongratsDialogController.prototype.setupButtonWithCountdownEffect = function () {
        var _this = this;
        if (!this.backBtn) {
            console.warn("CongratsDialogController: backBtn未设置，无法添加点击效果");
            return;
        }
        // 获取按钮的子节点
        var btnColorNormal = this.backBtn.getChildByName('btn_color_normal');
        var buttonLabel = this.backBtn.getChildByName('button_label');
        if (!btnColorNormal || !buttonLabel) {
            console.warn("CongratsDialogController: 按钮结构不完整，无法添加点击效果");
            return;
        }
        var label = buttonLabel.getComponent(cc.Label);
        var labelOutline = buttonLabel.getComponent(cc.LabelOutline);
        if (!label || !labelOutline) {
            console.warn("CongratsDialogController: 按钮标签组件不完整");
            return;
        }
        // 记录倒计时标签的原始字体大小
        var originalCountdownFontSize = 36;
        var originalCountdownLineHeight = 36;
        if (this.countdownTimeLabel) {
            originalCountdownFontSize = this.countdownTimeLabel.fontSize;
            originalCountdownLineHeight = this.countdownTimeLabel.lineHeight;
        }
        // 自定义按钮点击效果，同时控制倒计时标签
        Tools_1.Tools.setTouchEvent(btnColorNormal, 
        // 按下时：按钮和倒计时标签都变暗
        function (node) {
            // 按钮背景变暗
            Tools_1.Tools.setNodeSpriteFrame(node, Config_1.Config.btnGreenPressed);
            // 按钮文字变暗
            label.fontSize = 34;
            label.lineHeight = 34;
            var color = new cc.Color();
            cc.Color.fromHEX(color, Config_1.Config.btnGreenPressedColor);
            labelOutline.color = color;
            buttonLabel.color = cc.Color.fromHEX(new cc.Color(), '#B3B3B3');
            // 倒计时标签也变暗（字体缩小效果）
            if (_this.countdownTimeLabel && _this.countdownTimeLabel.node) {
                // 字体大小缩小（从原始大小缩小2）
                _this.countdownTimeLabel.fontSize = originalCountdownFontSize - 2;
                _this.countdownTimeLabel.lineHeight = originalCountdownLineHeight - 2;
                // 轮廓颜色变化（如果有LabelOutline组件）
                var countdownOutline = _this.countdownTimeLabel.getComponent(cc.LabelOutline);
                if (countdownOutline) {
                    var outlineColor = new cc.Color();
                    cc.Color.fromHEX(outlineColor, Config_1.Config.btnGreenPressedColor);
                    countdownOutline.color = outlineColor;
                }
                // 文字颜色变暗
                _this.countdownTimeLabel.node.color = cc.Color.fromHEX(new cc.Color(), '#B3B3B3');
            }
        }, 
        // 抬起时：恢复正常并执行点击逻辑
        function (node) {
            // 按钮背景恢复
            Tools_1.Tools.setNodeSpriteFrame(node, Config_1.Config.btnGreenNormal);
            // 按钮文字恢复
            label.fontSize = 36;
            label.lineHeight = 36;
            var color = new cc.Color();
            cc.Color.fromHEX(color, Config_1.Config.btnGreenNormalColor);
            labelOutline.color = color;
            buttonLabel.color = cc.Color.fromHEX(new cc.Color(), '#FFFFFF');
            // 倒计时标签恢复（恢复到原始大小）
            if (_this.countdownTimeLabel && _this.countdownTimeLabel.node) {
                // 字体大小恢复到原始大小
                _this.countdownTimeLabel.fontSize = originalCountdownFontSize;
                _this.countdownTimeLabel.lineHeight = originalCountdownLineHeight;
                // 轮廓颜色恢复（如果有LabelOutline组件）
                var countdownOutline = _this.countdownTimeLabel.getComponent(cc.LabelOutline);
                if (countdownOutline) {
                    var outlineColor = new cc.Color();
                    cc.Color.fromHEX(outlineColor, Config_1.Config.btnGreenNormalColor);
                    countdownOutline.color = outlineColor;
                }
                // 文字颜色恢复
                _this.countdownTimeLabel.node.color = cc.Color.fromHEX(new cc.Color(), '#FFFFFF');
            }
            // 执行点击逻辑
            _this.hide(true);
        }, 
        // 取消时：恢复正常
        function (node) {
            // 按钮背景恢复
            Tools_1.Tools.setNodeSpriteFrame(node, Config_1.Config.btnGreenNormal);
            // 按钮文字恢复
            label.fontSize = 36;
            label.lineHeight = 36;
            var color = new cc.Color();
            cc.Color.fromHEX(color, Config_1.Config.btnGreenNormalColor);
            labelOutline.color = color;
            buttonLabel.color = cc.Color.fromHEX(new cc.Color(), '#FFFFFF');
            // 倒计时标签恢复（恢复到原始大小）
            if (_this.countdownTimeLabel && _this.countdownTimeLabel.node) {
                // 字体大小恢复到原始大小
                _this.countdownTimeLabel.fontSize = originalCountdownFontSize;
                _this.countdownTimeLabel.lineHeight = originalCountdownLineHeight;
                // 轮廓颜色恢复（如果有LabelOutline组件）
                var countdownOutline = _this.countdownTimeLabel.getComponent(cc.LabelOutline);
                if (countdownOutline) {
                    var outlineColor = new cc.Color();
                    cc.Color.fromHEX(outlineColor, Config_1.Config.btnGreenNormalColor);
                    countdownOutline.color = outlineColor;
                }
                // 文字颜色恢复
                _this.countdownTimeLabel.node.color = cc.Color.fromHEX(new cc.Color(), '#FFFFFF');
            }
        });
    };
    __decorate([
        property(cc.Node)
    ], CongratsDialogController.prototype, "boardBg", void 0);
    __decorate([
        property(cc.Node)
    ], CongratsDialogController.prototype, "contentLay", void 0);
    __decorate([
        property(cc.Node)
    ], CongratsDialogController.prototype, "backBtn", void 0);
    __decorate([
        property(cc.Prefab)
    ], CongratsDialogController.prototype, "congratsItem", void 0);
    __decorate([
        property(cc.Node)
    ], CongratsDialogController.prototype, "layoutNode", void 0);
    CongratsDialogController = __decorate([
        ccclass
    ], CongratsDialogController);
    return CongratsDialogController;
}(cc.Component));
exports.default = CongratsDialogController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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