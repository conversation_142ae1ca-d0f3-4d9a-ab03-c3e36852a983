
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/BtnController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '0cfc6AzwvpKzLNWGyxSfqHD', 'BtnController');
// scripts/game/BtnController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var AudioManager_1 = require("../util/AudioManager");
var Config_1 = require("../util/Config");
var LocalStorageManager_1 = require("../util/LocalStorageManager");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var BtnController = /** @class */ (function (_super) {
    __extends(BtnController, _super);
    function BtnController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.settingBtn = null; // 设置按钮（固定不动）
        _this.settingPanel = null; // 设置面板容器
        _this.maskNode = null; // 遮罩节点，用于控制显示区域
        _this.gameplayBtn = null; // 玩法按钮
        _this.musicBtn = null; // 音乐开关按钮
        _this.soundBtn = null; // 音效开关按钮
        _this.infoDialogNode = null; // info_dialog节点引用
        // 音乐和音效开关状态
        _this.music = true;
        _this.sound = true;
        // 设置面板展开状态
        _this.isSettingExpanded = false;
        // 动画时长 - 可以调整这个值来控制动画速度
        _this.animationDuration = 0.5; // 增加到0.5秒，让动画更缓慢
        // 保存遮罩的原始高度
        _this.originalMaskHeight = 0;
        // 防止重复点击的标志
        _this.isAnimating = false;
        // 防止重复初始化音乐的标志
        _this.isMusicInitialized = false;
        // 全屏点击遮罩，用于点击屏幕任意位置关闭设置面板
        _this.clickMask = null;
        // 添加初始化标志，防止重复初始化
        _this.hasInitialized = false;
        return _this;
        // update (dt) {}
    }
    // LIFE-CYCLE CALLBACKS:
    BtnController.prototype.onLoad = function () {
        // 从本地存储获取音乐和音效开关状态
        this.music = LocalStorageManager_1.LocalStorageManager.GetInstance().getMusicSwitch();
        this.sound = LocalStorageManager_1.LocalStorageManager.GetInstance().getSoundSwitch();
        // 初始化设置面板为隐藏状态
        this.initSettingPanel();
        // 初始化点击遮罩
        this.initClickMask();
    };
    BtnController.prototype.start = function () {
        var _this = this;
        // 预加载所有按钮资源，然后再设置按钮事件
        this.preloadButtonResources(function () {
            // 添加一个小延迟，确保所有资源都完全就绪
            _this.scheduleOnce(function () {
                // 设置主设置按钮点击事件 - 使用新的按压UI样式，播放音效
                _this.setupImageButton(_this.settingBtn, 'side_btn_menu_normal', 'side_btn_menu_pressed', function () {
                    _this.toggleSettingPanel();
                }, true); // 改为true，播放音效来确认点击是否真的被执行
                // 设置玩法按钮点击事件 - 打开info_dialog页面
                _this.setupImageButton(_this.gameplayBtn, 'side_btn_info_normal', 'side_btn_info_pressed', function () {
                    _this.openInfoDialog();
                });
                // 设置音乐按钮点击事件 - 使用新的UI样式和开关功能
                _this.setupMusicButton();
                // 设置音效按钮点击事件 - 使用新的UI样式和开关功能
                _this.setupSoundButton();
                // 初始化按钮状态 - 使用新的按钮UI样式
                _this.updateMusicButtonUI();
                _this.updateSoundButtonUI();
                // 立即检查按钮状态和确保按钮在最上层
                _this.checkButtonStates();
                _this.ensureButtonsOnTop();
            }, 0.2); // 等待200毫秒
        });
        // 只在首次加载时播放音乐，避免重置正在播放的音乐
        this.scheduleOnce(function () {
            _this.initializeMusicPlayback();
        }, 0.1);
    };
    // 确保所有按钮都在最上层
    BtnController.prototype.ensureButtonsOnTop = function () {
        var buttons = [this.settingBtn, this.gameplayBtn, this.musicBtn, this.soundBtn];
        buttons.forEach(function (btn, index) {
            if (btn) {
                // 设置合理的zIndex值，确保按钮在所有棋盘和UI元素之上
                btn.zIndex = cc.macro.MAX_ZINDEX - 10 + index;
            }
        });
        // 如果有设置面板，确保它的zIndex也足够高，但低于按钮
        if (this.settingPanel) {
            this.settingPanel.zIndex = cc.macro.MAX_ZINDEX - 20;
        }
        if (this.maskNode) {
            this.maskNode.zIndex = cc.macro.MAX_ZINDEX - 19;
        }
    };
    // 检查按钮状态的调试方法
    BtnController.prototype.checkButtonStates = function () {
        var buttons = [
            { name: "设置按钮", node: this.settingBtn },
            { name: "玩法按钮", node: this.gameplayBtn },
            { name: "音乐按钮", node: this.musicBtn },
            { name: "音效按钮", node: this.soundBtn }
        ];
        buttons.forEach(function (btn) {
            if (btn.node) {
            }
            else {
                console.error("BtnController: " + btn.name + "\u8282\u70B9\u672A\u8BBE\u7F6E");
            }
        });
    };
    /**
     * 设置图片按钮 - 支持按压状态切换
     */
    BtnController.prototype.setupImageButton = function (node, normalImg, pressedImg, clickCallback, playSound) {
        var _this = this;
        if (playSound === void 0) { playSound = true; }
        if (!node) {
            console.error("BtnController: setupImageButton - 节点为空", normalImg);
            return;
        }
        // 确保节点可以接收触摸事件
        node.active = true;
        // 将按钮移动到最上层，确保不被其他UI元素遮挡
        node.zIndex = cc.macro.MAX_ZINDEX - 5;
        // 检查是否有Button组件阻挡触摸事件
        var buttonComponent = node.getComponent(cc.Button);
        if (buttonComponent) {
            buttonComponent.enabled = false;
        }
        // 强制确保节点具有正确的触摸属性
        node._touchListener = null; // 清除可能存在的旧监听器
        // 设置初始状态为normal（使用预加载的资源，应该能立即设置成功）
        this.setButtonSprite(node, normalImg);
        // 延迟一帧注册触摸事件，确保图片设置完成
        this.scheduleOnce(function () {
            _this.registerButtonEvents(node, normalImg, pressedImg, clickCallback, playSound);
            // 再次确保按钮在最上层
            node.zIndex = cc.macro.MAX_ZINDEX - 5;
        }, 0.1);
    };
    // 注册按钮触摸事件
    BtnController.prototype.registerButtonEvents = function (node, normalImg, pressedImg, clickCallback, playSound) {
        var _this = this;
        // 清除之前的事件监听器，避免重复注册
        node.off(cc.Node.EventType.TOUCH_START);
        node.off(cc.Node.EventType.TOUCH_END);
        node.off(cc.Node.EventType.TOUCH_CANCEL);
        // 添加触摸事件
        node.on(cc.Node.EventType.TOUCH_START, function () {
            // 按下时切换到pressed状态
            _this.setButtonSprite(node, pressedImg);
        }, this);
        node.on(cc.Node.EventType.TOUCH_END, function () {
            // 抬起时切换回normal状态并执行点击回调
            _this.setButtonSprite(node, normalImg);
            // 根据参数决定是否播放按钮点击音效
            if (playSound) {
                _this.playButtonClickSound();
            }
            if (clickCallback) {
                clickCallback();
            }
        }, this);
        node.on(cc.Node.EventType.TOUCH_CANCEL, function () {
            // 取消时也要切换回normal状态
            _this.setButtonSprite(node, normalImg);
        }, this);
    };
    /**
     * 设置按钮精灵图片 - 优先使用预加载的缓存资源
     */
    BtnController.prototype.setButtonSprite = function (node, imageName) {
        if (!node || !imageName) {
            console.error("BtnController: setButtonSprite - 参数无效", { node: !!node, imageName: imageName });
            return;
        }
        // 使用Config中定义的按钮资源路径，不包含扩展名
        var imagePath = Config_1.Config.buttonRes + imageName;
        // 优先从缓存获取（预加载的资源应该已经在缓存中）
        var cachedSpriteFrame = cc.resources.get(imagePath, cc.SpriteFrame);
        if (cachedSpriteFrame) {
            // 图片已经在缓存中，直接使用
            var sprite = node.getComponent(cc.Sprite);
            if (sprite) {
                sprite.spriteFrame = cachedSpriteFrame;
                node.color = cc.Color.WHITE;
                node.opacity = 255;
                return; // 成功设置，直接返回
            }
            else {
                console.error("BtnController: 节点没有Sprite组件", node.name);
                return;
            }
        }
        // 如果缓存中没有，说明预加载可能失败了，进行备用加载
        console.warn("BtnController: 缓存中没有找到图片，进行备用加载", imagePath);
        cc.resources.load(imagePath, cc.SpriteFrame, function (error, spriteFrame) {
            if (!error && spriteFrame && node && node.isValid) {
                var sprite = node.getComponent(cc.Sprite);
                if (sprite) {
                    sprite.spriteFrame = spriteFrame;
                    node.color = cc.Color.WHITE;
                    node.opacity = 255;
                }
                else {
                    console.error("BtnController: 节点缺少Sprite组件", node.name);
                }
            }
            else {
                console.error("BtnController: 备用加载按钮图片失败", {
                    imagePath: imagePath,
                    nodeValid: node && node.isValid,
                    error: error ? error.message : "未知错误"
                });
            }
        });
    };
    /**
     * 清理按钮状态 - 移除可能导致高亮效果的设置
     */
    BtnController.prototype.cleanButtonState = function (node) {
        if (!node)
            return;
        // 重置节点颜色和透明度
        node.color = cc.Color.WHITE;
        node.opacity = 255;
    };
    /**
     * 设置音乐按钮 - 支持开关状态和按压效果
     */
    BtnController.prototype.setupMusicButton = function () {
        var _this = this;
        if (!this.musicBtn)
            return;
        // 更新按钮显示状态
        this.updateMusicButtonUI();
        // 添加触摸事件
        this.musicBtn.on(cc.Node.EventType.TOUCH_START, function () {
            // 按下时显示pressed状态
            var currentImg = _this.music ? 'side_btn_music(on)_pressed' : 'side_btn_music(off)_pressed';
            _this.setButtonSprite(_this.musicBtn, currentImg);
        }, this);
        this.musicBtn.on(cc.Node.EventType.TOUCH_END, function () {
            // 播放按钮点击音效（音乐和音效按钮需要独立的音效播放）
            _this.playButtonClickSound();
            // 切换音乐状态 - 模仿SettingDialogController的实现
            _this.music = !_this.music;
            LocalStorageManager_1.LocalStorageManager.GetInstance().setMusicSwitch(_this.music);
            // 立即更新按钮UI到新状态的normal图片
            var newImgName = _this.music ? 'side_btn_music(on)_normal' : 'side_btn_music(off)_normal';
            _this.setButtonSprite(_this.musicBtn, newImgName);
            // 直接控制音乐播放，模仿SettingDialogController
            if (_this.music) {
                AudioManager_1.AudioManager.playBgm();
            }
            else {
                AudioManager_1.AudioManager.stopBgm();
            }
        }, this);
        this.musicBtn.on(cc.Node.EventType.TOUCH_CANCEL, function () {
            // 取消时恢复normal状态
            _this.updateMusicButtonUI();
        }, this);
    };
    /**
     * 设置音效按钮 - 支持开关状态和按压效果
     */
    BtnController.prototype.setupSoundButton = function () {
        var _this = this;
        if (!this.soundBtn)
            return;
        // 更新按钮显示状态
        this.updateSoundButtonUI();
        // 添加触摸事件
        this.soundBtn.on(cc.Node.EventType.TOUCH_START, function () {
            // 按下时显示pressed状态
            var currentImg = _this.sound ? 'side_btn_sound(on)_pressed' : 'side_btn_sound(off)_pressed';
            _this.setButtonSprite(_this.soundBtn, currentImg);
        }, this);
        this.soundBtn.on(cc.Node.EventType.TOUCH_END, function () {
            // 播放按钮点击音效（音乐和音效按钮需要独立的音效播放）
            _this.playButtonClickSound();
            // 切换音效状态
            _this.sound = !_this.sound;
            LocalStorageManager_1.LocalStorageManager.GetInstance().setSoundSwitch(_this.sound);
            // 立即更新按钮UI到新状态的normal图片
            var newImgName = _this.sound ? 'side_btn_sound(on)_normal' : 'side_btn_sound(off)_normal';
            _this.setButtonSprite(_this.soundBtn, newImgName);
        }, this);
        this.soundBtn.on(cc.Node.EventType.TOUCH_CANCEL, function () {
            // 取消时恢复normal状态
            _this.updateSoundButtonUI();
        }, this);
    };
    /**
     * 更新音乐按钮UI - 根据开关状态显示对应图片
     */
    BtnController.prototype.updateMusicButtonUI = function () {
        if (!this.musicBtn)
            return;
        var imgName = this.music ? 'side_btn_music(on)_normal' : 'side_btn_music(off)_normal';
        this.setButtonSprite(this.musicBtn, imgName);
    };
    /**
     * 更新音效按钮UI - 根据开关状态显示对应图片
     */
    BtnController.prototype.updateSoundButtonUI = function () {
        if (!this.soundBtn)
            return;
        var imgName = this.sound ? 'side_btn_sound(on)_normal' : 'side_btn_sound(off)_normal';
        this.setButtonSprite(this.soundBtn, imgName);
    };
    /**
     * 播放按钮点击音效 - 模仿项目中的音效播放方式
     */
    BtnController.prototype.playButtonClickSound = function () {
        // 检查音效开关状态，只有在音效开启时才播放
        if (this.sound) {
            // 使用AudioManager的按键音效方法
            AudioManager_1.AudioManager.keyingToneAudio();
        }
    };
    /**
     * 初始化音乐播放 - 只在首次加载时播放，避免重置
     */
    BtnController.prototype.initializeMusicPlayback = function () {
        // 只在未初始化时播放音乐
        if (!this.isMusicInitialized) {
            this.isMusicInitialized = true;
            // 检查音乐开关状态，只有开启时才播放
            if (this.music) {
                AudioManager_1.AudioManager.playBgm();
            }
        }
    };
    /**
     * 打开info_dialog页面 - 模仿项目中的实现方式
     */
    BtnController.prototype.openInfoDialog = function () {
        // 如果动画正在播放，禁止操作
        if (this.isAnimating) {
            return;
        }
        // 自动隐藏maskNode（收起设置面板）
        if (this.isSettingExpanded) {
            this.hideSettingPanel();
        }
        // 模仿HallPageController中的实现方式
        if (this.infoDialogNode) {
            var infoDialogController = this.infoDialogNode.getComponent("InfoDialogController");
            if (infoDialogController) {
                // 调用show方法，传入空的回调函数
                infoDialogController.show(function () { });
            }
        }
    };
    /**
     * 预加载所有按钮资源 - 确保按钮图片在设置事件前就已加载完成
     */
    BtnController.prototype.preloadButtonResources = function (callback) {
        // 定义所有需要预加载的按钮资源
        var buttonImages = [
            'side_btn_menu_normal',
            'side_btn_menu_pressed',
            'side_btn_info_normal',
            'side_btn_info_pressed',
            'side_btn_music(on)_normal',
            'side_btn_music(on)_pressed',
            'side_btn_music(off)_normal',
            'side_btn_music(off)_pressed',
            'side_btn_sound(on)_normal',
            'side_btn_sound(on)_pressed',
            'side_btn_sound(off)_normal',
            'side_btn_sound(off)_pressed'
        ];
        var loadedCount = 0;
        var totalCount = buttonImages.length;
        // 如果没有需要加载的资源，直接回调
        if (totalCount === 0) {
            callback();
            return;
        }
        // 预加载每个资源
        buttonImages.forEach(function (imageName) {
            var imagePath = Config_1.Config.buttonRes + imageName;
            // 先检查是否已经在缓存中
            var cachedSpriteFrame = cc.resources.get(imagePath, cc.SpriteFrame);
            if (cachedSpriteFrame) {
                loadedCount++;
                if (loadedCount >= totalCount) {
                    callback();
                }
            }
            else {
                // 加载资源
                cc.resources.load(imagePath, cc.SpriteFrame, function (error, spriteFrame) {
                    if (!error && spriteFrame) {
                    }
                    else {
                        console.error("BtnController: \u6309\u94AE\u8D44\u6E90\u52A0\u8F7D\u5931\u8D25 [" + (loadedCount + 1) + "/" + totalCount + "]", imageName, error ? error.message : "未知错误");
                    }
                    loadedCount++;
                    if (loadedCount >= totalCount) {
                        callback();
                    }
                });
            }
        });
    };
    /**
     * 初始化设置面板状态
     */
    BtnController.prototype.initSettingPanel = function () {
        // 如果已经初始化过，跳过
        if (this.hasInitialized) {
            return;
        }
        if (this.settingPanel) {
            // 设置面板初始为隐藏状态
            this.settingPanel.active = false;
        }
        // 重要：设置mask节点的初始状态为隐藏（高度为0）
        if (this.maskNode) {
            this.maskNode.height = 0;
            this.maskNode.opacity = 255;
        }
        // 设置初始状态
        this.isSettingExpanded = false;
        this.isAnimating = false;
        this.hasInitialized = true;
        // 确保点击遮罩在初始化时是禁用的
        this.disableClickMask();
    };
    /**
     * 切换设置面板的展开/收起状态
     */
    BtnController.prototype.toggleSettingPanel = function () {
        // 防止动画进行中的重复点击
        if (this.isAnimating) {
            console.log("BtnController: toggleSettingPanel - 动画进行中，忽略点击");
            return; // 添加return语句，防止继续执行
        }
        if (this.isSettingExpanded) {
            this.hideSettingPanel();
        }
        else {
            this.showSettingPanel();
        }
    };
    /**
     * 展开设置面板 - mask的size.y从0缓慢增加到275
     */
    BtnController.prototype.showSettingPanel = function () {
        var _this = this;
        if (!this.maskNode) {
            console.error("BtnController: showSettingPanel - maskNode为空");
            return;
        }
        // 立即更新状态，防止重复点击
        this.isAnimating = true;
        this.isSettingExpanded = true;
        // 显示settingPanel
        if (this.settingPanel) {
            this.settingPanel.active = true;
        }
        // 设置mask初始状态：高度为0，透明度为正常
        this.maskNode.height = 0;
        this.maskNode.opacity = 255;
        // 确保按钮始终在最上层
        this.ensureButtonsOnTop();
        // 执行展开动画 - mask高度从0到275
        cc.tween(this.maskNode)
            .to(this.animationDuration, {
            height: 275
        }, {
            easing: 'quartOut'
        })
            .call(function () {
            // 动画完成，解除动画锁定
            _this.isAnimating = false;
            // 启用点击遮罩，允许点击屏幕任意位置关闭面板
            _this.enableClickMask();
        })
            .start();
    };
    /**
     * 收起设置面板 - mask的size.y从275缓慢减少到0
     */
    BtnController.prototype.hideSettingPanel = function () {
        var _this = this;
        if (!this.maskNode) {
            return;
        }
        // 立即更新状态，防止重复点击
        this.isAnimating = true;
        this.isSettingExpanded = false;
        // 禁用点击遮罩
        this.disableClickMask();
        // 执行收起动画 - mask高度从275缓慢减少到0，同时添加渐隐效果
        cc.tween(this.maskNode)
            .to(this.animationDuration * 0.7, {
            height: 0
        }, {
            easing: 'quartOut'
        })
            .to(this.animationDuration * 0.3, {
            opacity: 0
        }, {
            easing: 'quartIn' // 最后阶段快速渐隐
        })
            .call(function () {
            // 动画完成后隐藏面板，解除动画锁定，恢复透明度
            if (_this.settingPanel) {
                _this.settingPanel.active = false;
            }
            _this.maskNode.opacity = 255; // 恢复透明度，为下次展开做准备
            _this.isAnimating = false;
            // 确保按钮始终在最上层
            _this.ensureButtonsOnTop();
        })
            .start();
    };
    /**
     * 初始化点击遮罩
     */
    BtnController.prototype.initClickMask = function () {
        // 创建一个全屏的透明遮罩节点
        this.clickMask = new cc.Node("ClickMask");
        // 添加Widget组件，让遮罩铺满整个屏幕
        var widget = this.clickMask.addComponent(cc.Widget);
        widget.isAlignTop = true;
        widget.isAlignBottom = true;
        widget.isAlignLeft = true;
        widget.isAlignRight = true;
        widget.top = 0;
        widget.bottom = 0;
        widget.left = 0;
        widget.right = 0;
        // 添加Button组件，确保能接收点击事件
        var button = this.clickMask.addComponent(cc.Button);
        button.transition = cc.Button.Transition.NONE; // 不要视觉过渡效果
        // 添加点击事件监听
        button.node.on('click', this.onClickMaskClick, this);
        // 添加到Canvas，但设置为不可见
        var canvas = cc.find('Canvas');
        if (canvas) {
            canvas.addChild(this.clickMask);
            // 设置为较低层级，但要高于棋盘等游戏元素
            this.clickMask.zIndex = 100;
        }
        // 默认禁用
        this.clickMask.active = false;
    };
    /**
     * 启用点击遮罩
     */
    BtnController.prototype.enableClickMask = function () {
        if (this.clickMask) {
            this.clickMask.active = true;
            console.log("BtnController: enableClickMask - 点击遮罩已启用");
        }
    };
    /**
     * 禁用点击遮罩
     */
    BtnController.prototype.disableClickMask = function () {
        if (this.clickMask) {
            this.clickMask.active = false;
            console.log("BtnController: disableClickMask - 点击遮罩已禁用");
        }
    };
    /**
     * 点击遮罩的处理函数
     */
    BtnController.prototype.onClickMaskClick = function () {
        console.log("BtnController: onClickMaskClick - 点击遮罩被触发", {
            isSettingExpanded: this.isSettingExpanded,
            isAnimating: this.isAnimating
        });
        // 检查面板是否展开且不在动画中
        if (this.isSettingExpanded && !this.isAnimating) {
            // 关闭设置面板，相当于再次点击设置按钮
            this.hideSettingPanel();
        }
        else {
            console.log("BtnController: onClickMaskClick - 条件不满足，忽略点击");
        }
    };
    BtnController.prototype.onDestroy = function () {
        // 清理点击遮罩
        if (this.clickMask && this.clickMask.isValid) {
            this.clickMask.off('click', this.onClickMaskClick, this);
            this.clickMask.removeFromParent();
            this.clickMask = null;
        }
    };
    __decorate([
        property(cc.Node)
    ], BtnController.prototype, "settingBtn", void 0);
    __decorate([
        property(cc.Node)
    ], BtnController.prototype, "settingPanel", void 0);
    __decorate([
        property(cc.Node)
    ], BtnController.prototype, "maskNode", void 0);
    __decorate([
        property(cc.Node)
    ], BtnController.prototype, "gameplayBtn", void 0);
    __decorate([
        property(cc.Node)
    ], BtnController.prototype, "musicBtn", void 0);
    __decorate([
        property(cc.Node)
    ], BtnController.prototype, "soundBtn", void 0);
    __decorate([
        property(cc.Node)
    ], BtnController.prototype, "infoDialogNode", void 0);
    BtnController = __decorate([
        ccclass
    ], BtnController);
    return BtnController;
}(cc.Component));
exports.default = BtnController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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