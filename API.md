# Minesweep 游戏 WebSocket API 文档

## 概述

本文档描述了 Minesweep 游戏的 WebSocket API 接口。所有消息都通过 WebSocket 连接进行通信。


12. 点击方块
WebSocket消息类型："ClickBlock"
玩家在扫雷游戏中点击方块进行操作。
使用场景：
- 在扫雷游戏中使用（支持方格地图mapType=0和六边形地图mapType=1）
- 在回合时间内（25秒）可以操作
- 每回合每个玩家只能操作一次，后续操作会覆盖前面的操作
请求参数
坐标参数根据地图类型而不同：
方格地图（mapType=0）
参数名	参数值	是否必填	参数类型	描述说明
x	-	是	Number	方块x坐标（0-7，从左到右）
y	-	是	Number	方块y坐标（0-7，从下到上，左下角为(0,0)）
action	-	是	Number	操作类型：1=挖掘方块，2=标记/取消标记地雷
六边形地图（mapType=1）
参数名	参数值	是否必填	参数类型	描述说明
x	-	是	Number	六边形q坐标（轴向坐标系统）
y	-	是	Number	六边形r坐标（轴向坐标系统）
action	-	是	Number	操作类型：1=挖掘方块，2=标记/取消标记地雷
注意：六边形地图中，x参数对应q坐标，y参数对应r坐标

## 数据结构定义

### MineBlock 结构

| 参数名 | 参数类型 | 描述说明 |
|--------|----------|----------|
| x | Number | x坐标（0-7） |
| y | Number | y坐标（0-7） |
| isRevealed | Boolean | 是否已揭开 |
| isMarked | Boolean | 是否被标记为地雷 |
| players | Array | 当前格子中的玩家ID列表 |
| NeighborMines | int | 周围地雷数量(0-8) |
| IsMine | bool | 是否是地雷 |

### RoomUser 结构

| 参数名 | 参数类型 | 描述说明 |
|--------|----------|----------|
| userId | String | 用户ID |
| nickName | String | 昵称 |
| avatar | String | 头像 |
| pos | Number | 座位号 |
| coin | Number | 玩家最新金币 |
| status | Number | 玩家状态（1-站立，2-已坐下，3-游戏中） |

**扫雷游戏相关字段（可选）**：

| 参数名 | 参数类型 | 描述说明 |
|--------|----------|----------|
| minesFound | Number | 已找到的地雷数量 |
| blocksRevealed | Number | 已揭开的安全块数量 |
| gameScore | Number | 当前游戏得分 |
| isAlive | Boolean | 是否还在游戏中（未踩到地雷） |

### InviteInfo 结构

| 参数名 | 参数类型 | 描述说明 |
|--------|----------|----------|
| inviteCode | Number | 邀请码 |
| playerNum | Number | 玩家人数 |
| mapType | Number | 地图类型（0-方格地图，1-六边形地图） |
| fee | Number | 房间费用 |
| users | Array | 邀请房间中的玩家列表 |

## 错误码说明

| 错误码 | 错误信息 | 描述 |
|--------|----------|------|
| 0 | 成功 | 操作成功 |
| 1 | 错误的GameId | 游戏ID错误 |
| 6 | 玩家已经在匹配队列中 | 重复匹配 |
| 7 | 没有足够的金币 | 金币不足 |
| 10 | 没有找到玩家信息 | 玩家不存在 |
| 13 | 请求参数错误 | 参数验证失败 |
| 21 | 游客被限制 | 游客无法进行此操作 |
| 32 | 玩家已经在游戏中了 | 重复进入游戏 |

## 游戏状态说明

| 状态值 | 状态名称 | 描述 |
|--------|----------|------|
| 1 | 先手 | 决定先手玩家 |
| 2 | 掷骰子 | 等待掷骰子或选择使用道具 |
| 3 | 移动棋子 | 移动棋子阶段 |
| 4 | 挑选道具 | 挑选道具阶段 |
| 5 | 使用道具 | 使用道具阶段 |

## 语聊房相关接口

### 17. 进入语聊房

**WebSocket消息类型**：`"EnterVoiceRoom"`

进入语聊房。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| roomId | - | 是 | String | app的语聊房Id |

#### 响应参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| platRoomID | - | 是 | String | 语聊房Id |
| playerNum | - | 是 | Number | 玩家人数 |
| fee | - | 是 | Number | 房间费 |
| users | - | 是 | Array | 玩家列表，参考VoiceRoomUser结构 |
| roomId | - | 是 | Number | 与之相关的游戏房间Id |
| kickOut | - | 是 | Boolean | 是否允许踢除玩家 |

### 18. 语聊房坐下

**WebSocket消息类型**：`"VoiceUserSit"`

在语聊房中坐下。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| roomId | - | 是 | String | app的语聊房Id |
| pos | - | 是 | Number | 请求坐下的游戏位置 |

#### 响应参数

成功时返回空对象 `{}`

### 19. 语聊房站起

**WebSocket消息类型**：`"VoiceUserStandUp"`

在语聊房中站起。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| roomId | - | 是 | String | app的语聊房Id |

#### 响应参数

成功时返回空对象 `{}`

### 20. 修改语聊房配置

**WebSocket消息类型**：`"ChangeVoiceCfg"`

修改语聊房配置。地图类型由服务端随机生成。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| roomId | - | 是 | String | app的语聊房Id |
| playerNum | - | 是 | Number | 玩家人数 |
| fee | - | 是 | Number | 房间费 |

#### 响应参数

成功时返回空对象 `{}`

### 21. 语聊房准备

**WebSocket消息类型**：`"VoiceUserReady"`

语聊房玩家准备。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| roomId | - | 是 | String | app的语聊房Id |
| ready | - | 是 | Boolean | 是否准备 |

#### 响应参数

成功时返回空对象 `{}`

### 22. 语聊房开始游戏

**WebSocket消息类型**：`"VoiceStartGame"`

语聊房开始游戏。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| roomId | - | 是 | String | app的语聊房Id |

#### 响应参数

成功时返回空对象 `{}`

### 23. 语聊房踢人

**WebSocket消息类型**：`"VoiceKickOut"`

语聊房踢除玩家。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| roomId | - | 是 | String | app的语聊房Id |
| userId | - | 是 | String | 玩家ID |

#### 响应参数

成功时返回空对象 `{}`

## 通知类消息

### 24. 匹配结果通知

**WebSocket消息类型**：`"GameStart"`

服务端通知客户端匹配到了其他玩家并开始游戏。此通知在 `PairRequest` 匹配成功后自动发送，包含完整的游戏房间信息和随机生成的地图类型。

**地图类型说明**：

- mapType=0：方形地图，响应中包含 `mapConfig` 字段
- mapType=1：六边形地图，响应中包含 `validHexCoords` 字段

**扫雷游戏流程**：

1. 发送 `GameStart` 通知
2. 前端播放2秒开场动画
3. 2秒后发送 `NoticeRoundStart` 通知，正式开始游戏

#### 通知参数

| 参数名         | 参数值     | 是否必填 | 参数类型 | 描述说明                                                    |
|--------|--------|----------|----------|----------|
| roomId         | 12345      | 是       | Number   | 房间ID                                                      |
| roomType       | 1          | 是       | Number   | 房间类型（1-普通场，2-私人场，3-语聊房）                    |
| playerNum      | 2-4        | 是       | Number   | 玩家人数                                                    |
| mapType        | 0/1        | 是       | Number   | 地图类型（0-方形地图，1-六边形地图）**由服务端随机生成**    |
| fee            | 0/500/1000 | 是       | Number   | 房间费                                                      |
| users          | -          | 是       | Array    | 匹配到的所有玩家，参考RoomUser结构                          |
| gameStatus     | 1          | 是       | Number   | 游戏状态                                                    |
| countDown      | 0          | 是       | Number   | 游戏状态倒计时                                              |
| validHexCoords | -          | 否       | Array    | 有效的六边形坐标列表（仅mapType=1时返回），参考HexCoord结构 |
| mapConfig      | -          | 否       | Object   | 地图配置信息（仅mapType=0时返回），参考MapConfig结构        |

#### MapConfig结构

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| width | 8 | 是 | Number | 地图宽度（8） |
| height | 8 | 是 | Number | 地图高度（8） |
| mineCount | 13 | 是 | Number | 地雷总数（13） |

#### HexCoord 结构（六边形地图专用）

| 参数名 | 参数值 | 参数类型 | 是否必填 | 描述说明          |
| ------ | ------ | -------- | -------- | ----------------- |
| q      | 0      | Number   | 是       | 六边形坐标系q坐标 |
| r      | 0      | Number   | 是       | 六边形坐标系r坐标 |



#### 响应示例

**方形地图响应示例（mapType=0）**：

```json
{
  "msgId": "GameStart",
  "code": 0,
  "msg": "success",
  "data": {
    "roomId": 12345,
    "roomType": 1,
    "playerNum": 2,
    "mapType": 0,
    "fee": 100,
    "users": [
      {
        "userId": "player_001",
        "nickName": "玩家1",
        "avatar": "avatar1.jpg",
        "pos": 1,
        "coin": 1000,
        "status": 3,
        "skinChessId": 1001
      }
    ],
    "gameStatus": 1,
    "countDown": 0,
    "mapConfig": {
      "width": 8,
      "height": 8,
      "mineCount": 13
    }
  }
}
```

**六边形地图响应示例（mapType=1）**：

```json
{
  "msgId": "GameStart",
  "code": 0,
  "msg": "success",
  "data": {
    "roomId": 12346,
    "roomType": 1,
    "playerNum": 2,
    "mapType": 1,
    "fee": 100,
    "users": [
      {
        "userId": "player_001",
        "nickName": "玩家1",
        "avatar": "avatar1.jpg",
        "pos": 1,
        "coin": 1000,
        "status": 3,
        "minesFound": 0,
        "blocksRevealed": 0,
        "gameScore": 0,
        "isAlive": true
      }
    ],
    "gameStatus": 1,
    "countDown": 300,
    "validHexCoords": [
      { "q": 0, "r": 0 },
      { "q": 1, "r": 0 },
      { "q": 1, "r": -1 },
      { "q": 0, "r": -1 },
      { "q": -1, "r": 0 },
      { "q": -1, "r": 1 },
      { "q": 0, "r": 1 }
    ]
  }
}
```

### 25. 扫雷回合开始通知

**WebSocket消息类型**：`"NoticeRoundStart"`

服务端通知客户端扫雷游戏回合开始。此通知在游戏开始2秒延迟后自动发送，标志着扫雷游戏正式开始。

**发送时机**：
- 在 `GameStart` 发送2秒后自动发送
- 后端同时开始25秒回合倒计时
- 前端收到此消息后立即进入游戏状态

**回合时间机制**：
- **前20秒**：玩家可以进行挖掘和标记操作
- **后5秒**：展示阶段，显示所有玩家操作但不允许新操作
- **第25秒**：回合正式结束，发送NoticeRoundEnd通知

**AI托管机制**：
- **触发时机**：第20秒时，为未选择格子的玩家执行AI托管
- **托管行为**：系统随机选择一格进行操作（70%概率挖掘，30%概率标记）
- **奖励规则**：AI托管状态下不享受首选玩家+1分奖励
- **优先级**：优先选择未被挖掘的格子，如无可用格子则随机选择
- **展示效果**：AI托管后立即在NoticeActionDisplay中显示所有玩家操作

#### 通知参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| roundNumber | 1 | 是 | Number | 回合编号（从1开始） |
| countDown | 25 | 是 | Number | 回合倒计时（25秒） |
| gameStatus | 0 | 是 | Number | 游戏状态（0-扫雷进行中） |

#### 响应示例

```json
{
  "msgId": "NoticeRoundStart",
  "code": 0,
  "msg": "success",
  "data": {
    "roundNumber": 1,
    "countDown": 25,
    "gameStatus": 0
  }
}
```

### 26. 扫雷操作展示通知

**WebSocket消息类型**：`"NoticeActionDisplay"`

服务端通知客户端进入操作展示阶段。此通知在第20秒时发送，在AI托管执行后显示所有玩家的操作状态。

**发送时机**：
- 当倒计时从21变为20时自动发送
- 先为未操作玩家执行AI托管，再发送此通知
- 显示所有玩家的操作状态（包括AI托管的操作）
- 不包含得分计算和地图状态更新
- 进入5秒展示阶段，不允许新操作

#### 通知参数
参数名	参数值	是否必填	参数类型	描述说明
roundNumber	1	是	Number	当前回合编号
gameStatus	0	是	Number	游戏状态（0-扫雷进行中）
countDown	5	是	Number	剩余倒计时（5秒展示阶段）
playerActions	-	是	Array	玩家操作结果列表，参考PlayerAction结构
floodFillResults	-	否	Array	连锁展开结果列表，参考FloodFillData结构
playerTotalScores	-	是	Object	玩家累计总得分对象，属性名为userID，属性值为分数(Number)
remainingMines	8	是	Number	剩余地雷数量
isGameEnded	true/false	是	Bool	游戏是否已结束
message	"展示阶段：显示所有玩家操作和得分"	是	String	提示信息
#### PlayerActionDisplay结构    
参数名	参数值	是否必填	参数类型	描述说明
userId	"1"	是	String	玩家ID
x	3	是	Number	操作坐标x
y	2	是	Number	操作坐标y
action	1	是	Number	操作类型（1-挖掘，2-标记）
score	10	是	Number	本次操作得分
isFirstChoice	TRUE	是	Boolean	是否为首选玩家
result	2/"mine"/"correct_mark"/"wrong_mark"	是	Number/String	操作结果（挖掘：数字或"mine"；标记："correct_mark"或"wrong_mark"）

#### 响应示例

```json
{
  "msgId": "NoticeActionDisplay",
  "code": 0,
  "msg": "success",
  "data": {
    "roundNumber": 1,
    "gameStatus": 0,
    "countDown": 5,
    "playerActions": [
      {
        "userId": "player_001",
        "x": 3,
        "y": 2,
        "action": 1
      },
      {
        "userId": "player_002",
        "x": 1,
        "y": 4,
        "action": 2
      }
    ],
    "message": "展示阶段：显示所有玩家操作"
  }
}
```

### 27. 扫雷回合结束通知

**WebSocket消息类型**：`"NoticeRoundEnd"`

服务端通知客户端扫雷游戏回合正式结束。此通知在25秒回合时间结束后自动发送，包含所有玩家的操作结果、基础得分计算和地图状态更新。

**发送时机**：
- 25秒回合倒计时结束后自动发送
- 包含所有玩家的操作结果和基础得分计算
- 展示已揭示的地图状态
- 开始5秒回合结束展示倒计时

**得分说明**：
- **只包含基础得分**：挖掘安全区(+6)、挖掘地雷(-12)、正确标记(+10)、错误标记(0)
- **不包含首选玩家奖励**：首选玩家的+1分已在NoticeFirstChoiceBonus中单独推送
- **用于回合总结**：显示本回合的基础操作得分情况

#### 通知参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| roundNumber | 1 | 是 | Number | 当前回合编号 |
| gameStatus | 1 | 是 | Number | 游戏状态（1-回合结束展示） |
| countDown | 5 | 是 | Number | 回合结束展示倒计时（5秒） |
| playerResults | - | 是 | Array | 玩家操作结果列表，参考PlayerRoundResult结构 |
| mapData | - | 是 | Array | 地图数据（只显示已揭示的方块） |
| floodFillResults | - | 否 | Array | 连锁展开结果列表，参考FloodFillResult结构 |

#### PlayerRoundResult结构

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| userId | "player_001" | 是 | String | 玩家ID |
| x | 3 | 是 | Number | 操作坐标x |
| y | 2 | 是 | Number | 操作坐标y |
| action | 1 | 是 | Number | 操作类型（1-挖掘，2-标记） |
| score | 7 | 是 | Number | 本回合得分 |
| isFirstChoice | true | 是 | Boolean | 是否为首选玩家（享受+1分奖励） |
| isMine | false | 是 | Boolean | 操作的方块是否是地雷 |
| neighborMines | 2 | 是 | Number | 操作方块周围的地雷数量 |

#### FloodFillResult结构

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| revealedBlocks | - | 是 | Array | 连锁揭示的方块列表，参考RevealedBlock结构 |
| totalRevealed | 5 | 是 | Number | 总共揭示的方块数 |
| triggerUserId | "player_001" | 是 | String | 触发连锁展开的玩家ID |
| triggerX | 3 | 是 | Number | 触发点X坐标 |
| triggerY | 2 | 是 | Number | 触发点Y坐标 |

#### RevealedBlock结构

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| x | 3 | 是 | Number | 方块X坐标 |
| y | 2 | 是 | Number | 方块Y坐标 |
| neighborMines | 1 | 是 | Number | 周围地雷数量 |
| isMine | false | 是 | Boolean | 是否是地雷 |
| triggerUserId | "player_001" | 是 | String | 触发揭示的玩家ID |

#### 响应示例

```json
{
  "msgId": "NoticeRoundEnd",
  "code": 0,
  "msg": "success",
  "data": {
    "roundNumber": 1,
    "gameStatus": 1,
    "countDown": 5,
    "playerResults": [
      {
        "userId": "player_001",
        "x": 3,
        "y": 2,
        "action": 1,
        "score": 7,
        "isFirstChoice": true
      },
      {
        "userId": "player_002",
        "x": 1,
        "y": 4,
        "action": 2,
        "score": 10,
        "isFirstChoice": false
      }
    ],
    "mapData": [
      [
        {"x": 0, "y": 0, "isRevealed": false, "isMarked": false, "players": []},
        {"x": 1, "y": 0, "isRevealed": false, "isMarked": false, "players": []}
      ]
    ]
  }
}
```

### 28. 扫雷首选玩家奖励推送通知

**WebSocket消息类型**：`"NoticeFirstChoiceBonus"`

服务端通知客户端首选玩家奖励。此通知在玩家成为首选玩家（第一个在当前回合进行挖掘或标记操作）时立即发送，只包含首选玩家的+1分奖励。

**发送时机**：
- 玩家成为首选玩家时立即发送
- 只发送给首选玩家本人
- 只包含+1分的首选玩家奖励，不包含基础得分
- 与基础得分（挖掘/标记得分）分离推送

**奖励规则**：
- **首选玩家奖励**：固定+1分
- **仅真实玩家操作**：AI托管不享受此奖励
- **无论操作对错**：不管挖掘/标记结果如何都给奖励
- **每回合只有一个**：只有第一个操作的玩家享受

#### 通知参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| userId | "player_001" | 是 | String | 玩家ID |
| roundNumber | 1 | 是 | Number | 回合编号 |
| bonusScore | 1 | 是 | Number | 首选玩家奖励分数（固定+1） |
| totalScore | 15 | 是 | Number | 累计总得分（包含此奖励） |

#### 响应示例

```json
{
  "msgId": "NoticeFirstChoiceBonus",
  "code": 0,
  "msg": "success",
  "data": {
    "userId": "player_001",
    "roundNumber": 1,
    "bonusScore": 1,
    "totalScore": 15
  }
}
```

### 29. 扫雷游戏结束通知

**WebSocket消息类型**：`"NoticeGameEnd"`

服务端通知客户端扫雷游戏结束。此通知在满足游戏结束条件后发送，包含最终排名和完整地图信息。

**游戏结束条件**（满足任一条件即可）：
1. **所有安全区被挖掘**：所有非地雷格子都被至少一个玩家挖掘
2. **剩余地雷全部标记准确**：
   - 所有未挖掘的地雷都被正确标记
   - 没有安全区被错误标记为地雷

**发送时机**：
- 满足游戏结束条件后立即发送
- 包含最终排名和总得分
- 显示完整的地图信息（包括所有地雷位置）
- 开始10秒游戏结束展示倒计时

#### 通知参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| gameStatus | 2 | 是 | Number | 游戏状态（2-游戏结束） |
| countDown | 10 | 是 | Number | 游戏结束展示倒计时（10秒） |
| finalRanking | [] | 是 | Array | 最终排名列表，参考PlayerFinalResult结构 |
| completeMapData | [] | 是 | Array | 完整地图数据（显示所有地雷和信息） |
| totalRounds | 1 | 是 | Number | 总回合数 |

#### PlayerFinalResult结构

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| userId | "player_001" | 是 | String | 玩家ID |
| totalScore | 15 | 是 | Number | 总得分 |
| rank | 1 | 是 | Number | 最终排名 |

#### 响应示例

```json
{
  "msgId": "NoticeGameEnd",
  "code": 0,
  "msg": "success",
  "data": {
    "gameStatus": 2,
    "countDown": 10,
    "finalRanking": [
      {
        "userId": "player_001",
        "totalScore": 15,
        "rank": 1
      },
      {
        "userId": "player_002",
        "totalScore": 12,
        "rank": 2
      }
    ],
    "completeMapData": [
      [
        {"x": 0, "y": 0, "isMine": false, "isRevealed": true, "isMarked": false, "neighborMines": 2, "players": ["player_001"]},
        {"x": 1, "y": 0, "isMine": true, "isRevealed": false, "isMarked": true, "neighborMines": 0, "players": ["player_002"]}
      ]
    ],
    "totalRounds": 1
  }
}
```

### 29. 金币变化通知

**WebSocket消息类型**：`"NoticeUserCoin"`

服务端通知客户端玩家金币变化。

#### 通知参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| userId | - | 是 | String | 玩家ID |
| coin | - | 是 | Number | 玩家最新的金币 |

## 数据结构定义

### VoiceRoomUser 结构

| 参数名 | 参数类型 | 描述说明 |
|--------|----------|----------|
| userId | String | 玩家id |
| nickName | String | 昵称 |
| avatar | String | 头像 |
| pos | Number | 座位号 |
| ready | Boolean | 是否准备 |
| robot | Boolean | 是否机器人 |
| role | String | 角色 |

### RoomUser 结构

参数名	参数类型	是否必填	描述说明
userId	String	是	用户ID
nickName	String	是	昵称
avatar	String	是	头像
pos	Number	是	座位号
coin	Number	是	玩家最新金币
status	Number	是	玩家状态（1-站立，2-已坐下，3-游戏中）
gameScore	Number	是	当前游戏得分
isAlive	Boolean	是	是否还在游戏中
isAIManaged	Boolean	是	是否处于AI托管状态，true表示当前由AI代为操作，false表示玩家手动操作（仅联机模式下且玩家处于AI托管状态时出现）


**扫雷游戏相关字段（可选）**：

| 参数名 | 参数类型 | 是否必填 | 描述说明 |
|--------|----------|----------|----------|
| minesFound | Number | 否 | 已找到的地雷数量 |
| blocksRevealed | Number | 否 | 已揭开的安全块数量 |
| gameScore | Number | 否 | 当前游戏得分 |
| isAlive | Boolean | 否 | 是否还在游戏中（未踩到地雷） |

### InviteInfo 结构

| 参数名 | 参数类型 | 描述说明 |
|--------|----------|----------|
| inviteCode | Number | 邀请码 |
| playerNum | Number | 玩家人数 |
| mapType | Number | 地图类型（0-方格地图，1-六边形地图） |
| fee | Number | 房间费用 |
| users | Array | 邀请房间中的玩家列表 |

## 错误码说明

| 错误码 | 错误信息 | 描述 |
|--------|----------|------|
| 0 | 成功 | 操作成功 |
| 1 | 错误的GameId | 游戏ID错误 |
| 6 | 玩家已经在匹配队列中 | 重复匹配 |
| 7 | 没有足够的金币 | 金币不足 |
| 10 | 没有找到玩家信息 | 玩家不存在 |
| 13 | 请求参数错误 | 参数验证失败 |
| 21 | 游客被限制 | 游客无法进行此操作 |
| 32 | 玩家已经在游戏中了 | 重复进入游戏 |

## 游戏状态说明

**扫雷游戏状态（mapType=0）**：

| 状态值 | 状态名称 | 描述 |
|--------|----------|------|
| 0 | 扫雷进行中 | 扫雷游戏进行中，玩家可以进行操作 |
| 1 | 回合结束展示 | 回合结束，展示所有玩家操作结果 |
| 2 | 游戏结束 | 扫雷游戏已结束 |

## 地图类型说明

| 类型值 | 类型名称 | 描述 |
|--------|----------|----------|
| 0 | 方格地图 | 8×8方格布局，13个地雷随机分布 |
| 1 | 六边形地图 | 每一块为六边形布局的地图 |

## 扫雷游戏操作类型说明

| 操作值 | 操作名称 | 描述 |
|--------|----------|------|
| 1 | 挖掘 | 挖掘方块，揭示内容（安全区+6分，地雷-12分） |
| 2 | 标记地雷 | 标记或取消标记地雷（正确标记+10分，错误标记+0分） |

## 扫雷游戏时间配置说明

| 配置项 | 时间（秒） | 描述 |
|--------|------------|------|
| 游戏开始延迟 | 2 | GameStart后的展示时间 |
| 回合总时间 | 25 | 每回合的总操作时间 |
| 隐藏期 | 20 | 前20秒只能看到自己的操作 |
| 展示期 | 5 | 后5秒显示所有人的操作 |

## 扫雷游戏得分规则说明

| 操作结果 | 得分 | 描述 |
|----------|------|------|
| 挖掘安全区 | +6分 | 成功挖掘到非地雷方块 |
| 挖掘地雷区 | -12分 | 不幸挖掘到地雷 |
| 正确标记地雷 | +10分 | 标记的方块确实是地雷 |
| 错误标记 | +0分 | 标记的方块不是地雷 |

**注意**：
- 多个玩家可以选择同一个方块，都会获得相同的得分
- 每回合每个玩家只能操作一次
- 游戏结束条件：所有非地雷方块都被至少一个玩家挖掘过

29. 扫雷游戏结算通知
WebSocket消息类型："Settlement"
服务端通知客户端扫雷游戏结算结果。此通知在游戏结束时发送，包含金币分配和游戏统计信息。
发送时机：
- 游戏结束后立即发送
- 在NoticeGameEnd之前发送
- 包含金币变化和奖励分配信息
- 提供游戏统计数据
通知参数
参数名	参数值	是否必填	参数类型	描述说明
gameType	"minesweeper"	是	String	游戏类型标识
totalRounds	5	是	Number	总回合数
playerCount	2	是	Number	玩家数量
finalRanking	-	是	Array	最终排名列表，参考PlayerFinalResult结构
fee	100	是	Number	房间费用
gameStats	-	是	Object	游戏统计信息
GameStats结构
参数名	参数值	是否必填	参数类型	描述说明
mapSize	"8x8"	是	String	地图大小
mineCount	13	是	Number	地雷总数
revealedCount	45	是	Number	已揭示的方块数



17.获取关卡进度
WebSocket消息类型："ExtendLevelProgress"
获取用户的关卡进度统计信息。
请求参数
无参数
响应参数
参数名	参数值	是否必填	参数类型	描述说明
totalLevels	30	是	Number	总关卡数
clearedLevels	5	是	Number	已通关关卡数
currentLevel	6	是	Number	当前应挑战的关卡

15.获取关卡详情
WebSocket消息类型："ExtendLevelInfo"
获取指定关卡的详细信息，包含完整的地图配置。
请求参数
参数名	参数值	是否必填	参数类型	描述说明
levelId	1月30日	是	Number	关卡编号
响应参数
返回单个LevelInfo结构，包含完整的地图配置信息。

LevelInfo结构
参数名	参数值	是否必填	参数类型	描述说明
levelId	1月30日	是	Number	关卡编号
mapType	0/1	是	Number	地图类型（0=方格地图，1=六边形地图）
mapWidth	8月10日	否	Number	地图宽度（仅方格地图）
mapHeight	8月10日	否	Number	地图高度（仅方格地图）
validHexes	-	否	Array	六边形有效坐标（仅六边形地图），参考HexCoord结构
neighborMap	-	否	Object	六边形邻居关系（仅六边形地图）
mineCount	4月25日	是	Number	地雷数量
isSpecial	true/false	是	Boolean	是否特殊关卡
isUnlocked	true/false	是	Boolean	是否已解锁
isCleared	true/false	是	Boolean	是否已通关

提交关卡通关
WebSocket消息类型："ExtendLevelComplete"
提交关卡通关结果，记录通关状态并检查是否解锁下一关。
请求参数
参数名	参数值	是否必填	参数类型	描述说明
levelId	1月30日	是	Number	关卡编号
isSuccess	true/false	是	Boolean	是否通关成功
响应参数
参数名	参数值	是否必填	参数类型	描述说明
success	true/false	是	Boolean	提交是否成功
newUnlocked	6	否	Number	新解锁的关卡编号（如有）
userProgress	5	是	Number	用户当前进度
message	"恭喜通关！"	是	String	提示信息

单机-关卡模式游戏接口
32.开始关卡游戏
WebSocket消息类型："ExtendLevelInfo"
开始指定关卡的游戏，创建游戏房间并返回关卡详情和游戏信息。
请求参数
参数名	参数值	是否必填	参数类型	描述说明
levelId	1月30日	是	Number	关卡编号
响应参数
参数名	参数值	是否必填	参数类型	描述说明
levelId	1	是	Number	关卡编号
mapType	0	是	Number	地图类型（0=方格，1=六边形）
mapWidth	8	否	Number	地图宽度（方格地图用）
mapHeight	8	否	Number	地图高度（方格地图用）
validHexes	[...]	否	Array	有效六边形坐标（六边形地图用）
neighborMap	{...}	否	Object	邻居关系映射（六边形地图用）
mineCount	8	是	Number	地雷数量
isSpecial	true/false	是	Boolean	是否特殊关卡
isUnlocked	true/false	是	Boolean	是否已解锁
isCleared	true/false	是	Boolean	是否已通关
roomId	1640995200123	是	Number	游戏房间ID（用于断线重连）
gameStatus	2	是	Number	游戏状态
countDown	-1	是	Number	倒计时（关卡模式固定为-1）

33.关卡点击方块
WebSocket消息类型："LevelClickBlock"
关卡模式专用的点击方块接口，与联机模式完全独立。支持自由的标记状态切换和即时操作响应。
接口特点：
- 独立接口：与联机模式的ClickBlock接口完全分离
- 即时响应：操作立即生效，无延迟展示
- 自由标记：支持标记状态的自由切换（标记↔取消标记）
- 无时间限制：没有回合制和倒计时限制
- 单人游戏：专为关卡模式设计的单人操作
请求参数
坐标参数根据地图类型而不同：
方格地图（mapType=0）
参数名	参数值	是否必填	参数类型	描述说明
x	1	是	Number	方块x坐标（0-7，从左到右）
y	1	是	Number	方块y坐标（0-7，从下到上，左下角为(0,0)）
action	1/2	是	Number	操作类型：1=挖掘方块，2=标记/取消标记地雷
六边形地图（mapType=1）
参数名	参数值	是否必填	参数类型	描述说明
q	1	是	Number	六边形q坐标（轴向坐标系统）
r	2	是	Number	六边形r坐标（轴向坐标系统）
action	1/2	是	Number	操作类型：1=挖掘方块，2=标记/取消标记地雷
注意：六边形地图的坐标通过x和y参数传递，其中x对应q坐标，y对应r坐标。域

响应参数
参数名	参数值	是否必填	参数类型	描述说明
success	true/false	是	Boolean	操作是否成功
x	3	是	Number	操作坐标x（六边形地图中对应q坐标）
y	2	是	Number	操作坐标y（六边形地图中对应r坐标）
action	1	是	Number	操作类型（1-挖掘，2-标记）
result	2/"mine"/"marked"/"unmarked"	是	Number/String	操作结果
floodFillResults	-	否	Array	连锁展开结果列表，参考FloodFillResult结构
remainingMines	8	是	Number	剩余地雷数量
gameStatus	2	是	Number	游戏状态（2-进行中，3-胜利，4-失败）
message	"操作成功"	是	String	提示信息



操作结果说明
- 挖掘操作：
  - 返回周围地雷数（0-8或0-6）
  - 如果是地雷则返回"mine"
  - 空白方块会触发连锁展开
- 标记操作：
  - 返回"marked"（已标记）或"unmarked"（已取消标记）
  - 不透露该位置是否为地雷，保持游戏悬念
  - 支持自由切换标记状态
标记功能特点
- 自由切换：可以在标记和未标记状态之间自由切换
- 状态保护：已挖掘的方块无法标记，已标记的方块无法挖掘
- 悬念保持：标记操作不会透露方块是否为地雷
- 即时生效：标记操作立即生效，无需等待




35.关卡游戏结束通知
WebSocket消息类型："LevelGameEnd"
服务端在关卡游戏自然结束时（胜利或失败）自动推送的通知消息。
发送时机：
- 踩雷失败时自动推送
- 完成所有非地雷方块时自动推送
- 在ClickBlock响应之后立即推送
- 自动更新用户进度（胜利时）
通知参数
参数名	参数值	是否必填	参数类型	描述说明
levelId	1	是	Number	关卡编号
isSuccess	true/false	是	Boolean	是否胜利d
gameTime	300	是	Number	游戏时长（秒）
message	"恭喜通关第1关！"	是	String	提示信息
nextLevelId	2	否	Number	下一关编号（胜利且有下一关时）

36.AI托管状态变更通知
AIStatusChange

服务端通知客户端AI托管状态发生变更。当房间内任何玩家进入或退出AI托管时，会向所有玩家广播此通知。
系统特点：
- 仅联机模式：关卡模式不支持AI托管
- 自动触发：倒计时剩余5秒时，系统自动检查未操作的玩家并进入AI托管
- 自动退出：玩家进行任何操作（点击方块）时自动退出托管
- 随机决策：AI托管使用纯随机决策，不使用智能算法
- 状态广播：托管状态变更会实时广播给房间内所有玩家
触发时机
自动进入AI托管：
- 回合倒计时剩余8秒时，系统自动检查未操作的玩家
- 如果玩家在当前回合未进行任何操作，自动进入AI托管
自动退出AI托管：
- 玩家进行任何点击方块操作时，立即退出AI托管状态
通知参数
参数名	参数值	是否必填	参数类型	描述说明
userId	"user123"	是	String	状态变更的用户ID
isAIManaged	TRUE	是	Boolean	是否进入AI托管（true=进入，false=退出）
timestamp	1640995200	是	Number	状态变更时间戳


37.取消AI托管
WebSocket消息类型："CancelAIManagement"
使用场景：用于取消托管状态
请求参数：无
响应参数：由AI托管状态变更通知进行广播响应，消息类型为AIStatusChange
通知参数
参数名	参数值	是否必填	参数类型	描述说明
userId	"user123"	是	String	状态变更的用户ID
isAIManaged	TRUE	是	Boolean	是否进入AI托管（true=进入，false=退出）
timestamp	1640995200	是	Number	状态变更时间戳


38.显示地雷位置
WebSocket消息类型："DebugShowMines"
⚠️ 注意：这是调试接口，仅用于开发测试，生产环境应禁用
用于查看当前地图中所有地雷的位置，帮助验证地雷分布和游戏逻辑。
使用场景
- 开发阶段验证地雷生成算法
- 测试游戏逻辑正确性
- 调试地图配置问题
权限控制
- 联机模式和关卡模式均可用
- 无需特殊权限验证（调试用途）
请求参数
无需参数，直接发送消息即可。
响应参数
参数名"	参数值	是否必填	参数类型	描述说明
mines	地雷位置数组	是	Array	所有地雷的坐标位置


10. 断线重连接口
WebSocket消息类型："EnterRoom"（联机模式）或 "ExtendLevelInfo"（关卡模式）
当用户登录后，如果检测到用户之前在游戏中（roomId > 0），系统会自动触发断线重连机制。用户发送进入房间或获取关卡信息的消息时，服务端会检查是否需要断线重连，并返回相应的游戏状态。
触发条件
1. 用户登录时返回的 roomId > 0
2. 用户发送 EnterRoom（联机模式）或 ExtendLevelInfo（关卡模式）消息
3. 服务端检测到用户有有效的游戏状态
联机模式断线重连
请求参数：按照正常的 EnterRoom 消息格式
响应参数：
联机模式断线重连返回标准的EnterRoom响应格式，主要字段包括：
参数名	参数值	是否必填	参数类型	描述说明
roomId	-	是	Number	房间ID
roomType	-	是	Number	房间类型
playerNum	-	是	Number	玩家人数
mapType	-	是	Number	地图类型（0:方格 1:六边形）
fee	-	是	Number	房间费
users	-	是	Array	玩家列表，参考RoomUser结构
gameStatus	-	是	Number	游戏状态
countDown	-	是	Number	倒计时秒数
tokenInfo	-	是	Object	当前Token信息
validHexCoords	-	否	Array	有效的六边形坐标（六边形地图时使用）
mapData	-	否	Object	地图数据（包含已挖掘信息）
isOnlineMode	TRUE	是	Boolean	游戏模式标志（true-联机模式，false-关卡模式）
注意：联机模式断线重连返回的是完整的EnterRoom响应结构，详细字段定义请参考正常的EnterRoom接口文档。
RevealedBlockInfo结构
参数名	参数值	是否必填	参数类型	描述说明
x	-	是	Number	X坐标
y	-	是	Number	Y坐标
q	-	否	Number	六边形Q坐标（仅六边形地图）
r	-	否	Number	六边形R坐标（仅六边形地图）
neighborMines	-	是	Number	周围地雷数
MarkedBlockInfo结构
参数名	参数值	是否必填	参数类型	描述说明
x	-	是	Number	X坐标
y	-	是	Number	Y坐标
q	-	否	Number	六边形Q坐标（仅六边形地图）
r	-	否	Number	六边形R坐标（仅六边形地图）
关卡模式（ExtendLevelInfo响应）
请求参数：不用带参数，直接发送
响应参数：
ExtendLevelInfo接口既用于开始新游戏，也用于断线重连，响应格式统一：
参数名	参数值	是否必填	参数类型	描述说明
reconnected	true/false	是	Boolean	断线重连标识（新游戏为false，断线重连为true）
isOnlineMode	FALSE	是	Boolean	游戏模式标志（关卡模式固定为false）
levelId	-	是	Number	关卡ID
gameStatus	-	是	Number	游戏状态（0:等待开始 1:扫雷进行中 4:关卡胜利 5:关卡失败）
mapType	-	是	Number	地图类型（0:方格 1:六边形）
roomId	-	是	Number	游戏房间ID
mineCount	-	是	Number	地雷总数
remainingMines	-	否	Number	剩余地雷数（仅断线重连时返回）
mineMap	-	否	Object	地图状态信息（仅断线重连时返回），参考LevelMineMapInfo结构
LevelMineMapInfo结构
参数名	参数值	是否必填	参数类型	描述说明
width	-	是	Number	地图宽度
height	-	是	Number	地图高度
mineCount	-	是	Number	地雷总数
revealedBlocks	-	是	Array	已挖掘的方块，参考RevealedBlockInfo结构
markedBlocks	-	是	Array	已标记的方块，参考MarkedBlockInfo结构
注意：RevealedBlockInfo 和 MarkedBlockInfo 结构与联机模式相同，详见上方结构定义。

统一游戏状态值（所有接口使用相同的状态值体系）：
- 0 = 等待开始
- 1 = 扫雷进行中
- 2 = 回合结束展示（仅联机模式）
- 3 = 游戏结束（联机模式）
- 4 = 关卡胜利（关卡模式）
- 5 = 关卡失败（关卡模式）