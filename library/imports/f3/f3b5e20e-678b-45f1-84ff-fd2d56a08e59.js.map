{"version": 3, "sources": ["assets/scripts/game/CongratsDialogController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAItF,iDAAgD;AAChD,qDAAkD;AAClD,6CAA4C;AAC5C,0DAAwE;AACxE,wEAAmE;AACnE,yCAAwC;AACxC,uCAAsC;AAEhC,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAE5C,MAAM;AAEN;IAAsD,4CAAY;IAAlE;QAAA,qEAuUC;QApUG,aAAO,GAAY,IAAI,CAAA;QAEvB,gBAAU,GAAY,IAAI,CAAA;QAE1B,aAAO,GAAY,IAAI,CAAA;QAEvB,kBAAY,GAAc,IAAI,CAAC,CAAA,UAAU;QAElC,gBAAU,GAAY,IAAI,CAAC,CAAA,SAAS;QAG3C,wBAAkB,GAAa,IAAI,CAAA;QACnC,uBAAiB,GAAW,IAAI,CAAC,CAAA,SAAS;QAE1C,kBAAY,GAAa,IAAI,CAAA,CAAC,SAAS;QACvC,aAAO,GAAW,EAAE,CAAC,CAAA,UAAU;;IAqTnC,CAAC;IAlTG,yCAAM,GAAN;QACI,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;IACrG,CAAC;IACS,2CAAQ,GAAlB;QACI,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACxC,4CAA4C;IAChD,CAAC;IAED,wCAAK,GAAL;QAAA,iBAWC;QAVG,gBAAgB;QAChB,aAAK,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE;YAC5B,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACnB,CAAC,CAAC,CAAA;QAED,eAAe;QACf,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAEjC,iCAAiC;QACjC,IAAI,CAAC,8BAA8B,EAAE,CAAC;IAC3C,CAAC;IAGD,uCAAI,GAAJ,UAAK,gBAAkC,EAAE,YAAsB;QAC3D,IAAI,CAAC,YAAY,GAAG,YAAY,CAAA;QAChC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;QACvB,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,CAAA;QAErB,6BAA6B;QAC7B,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAGlC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAA;QAC/B,SAAS;QACT,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;aACjB,EAAE,CAAC,eAAM,CAAC,eAAe,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;aACxC,KAAK,EAAE,CAAC;IACjB,CAAC;IAED,2CAAQ,GAAR,UAAS,gBAAkC;QACvC,sCAAsC;QACtC,IAAI,QAAQ,GAAG,gBAAgB,CAAC,YAAY,IAAI,gBAAgB,CAAC,KAAK,CAAC;QAEvE,aAAa;QACb,IAAI,CAAC,gBAAgB,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC5D,OAAO,CAAC,IAAI,CAAC,0BAA0B,EAAE,gBAAgB,CAAC,CAAC;YAC3D,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU;YACnC,OAAO;SACV;QAED,IAAM,aAAa,GAAG,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC;QACzE,IAAM,KAAK,GAAG,QAAQ,CAAC,SAAS,CAAC,UAAC,IAAI,IAAK,OAAA,IAAI,CAAC,MAAM,KAAK,aAAa,EAA7B,CAA6B,CAAC,CAAC,CAAA,IAAI;QAC9E,IAAI,KAAK,IAAI,CAAC,EAAE;YACZ,2CAA2C;YAC3C,IAAI,MAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;gBAC3B,iCAAiC;gBACjC,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,GAAI,QAAQ,CAAC,KAAK,CAAS,CAAC,IAAI,CAAC;aACpF;iBAAM,IAAI,SAAS,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;gBACrC,yCAAyC;gBACzC,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,IAAK,QAAQ,CAAC,KAAK,CAAS,CAAC,OAAO,CAAC;aACxF;SACJ;QAED,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC;gCAC3B,CAAC;YACN,IAAM,IAAI,GAAG,EAAE,CAAC,WAAW,CAAC,OAAK,YAAY,CAAC,CAAC;YAC/C,IAAM,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YACzB,OAAK,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAC/B,UAAU,CAAC;gBACP,IAAI,CAAC,YAAY,CAAC,gCAAsB,CAAC,CAAC,UAAU,CAAC,IAAI,EAAE,uBAAU,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YAC/G,CAAC,EAAE,GAAG,CAAC,CAAC;;;QANZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC;oBAA/B,CAAC;SAOT;QACD,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,CAAA,CAAA,UAAU;IACrC,CAAC;IAED,oBAAoB;IACpB,uCAAI,GAAJ,UAAK,IAAqB;QAA1B,iBAoBC;QApBI,qBAAA,EAAA,YAAqB;QACtB,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,EAAE,CAAA;SACtB;QACD,iBAAO,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QAC7B,SAAS;QACT,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;aACjB,EAAE,CAAC,eAAM,CAAC,eAAe,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;aACxC,IAAI,CAAC;YACF,KAAI,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;YACxB,IAAI,IAAI,EAAE;gBACN,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,EAAE,CAAA;gBACpC,IAAI,eAAe,GAAoB;oBACnC,OAAO,EAAE,+BAAa,CAAC,YAAY;oBACnC,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,CAAA,WAAW;iBACnC,CAAA;gBACD,iBAAO,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAS,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;aAC9D;QACL,CAAC,CAAC;aACD,KAAK,EAAE,CAAC;IACjB,CAAC;IACS,4CAAS,GAAnB;QACI,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;SACzC;IAEL,CAAC;IAED,iDAAc,GAAd,UAAe,OAAe;QAA9B,iBAkBC;QAjBG,kBAAkB;QAClB,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACjC,IAAI,gBAAgB,GAAG,OAAO,CAAC;QAC/B,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;QAE5C,IAAI,CAAC,iBAAiB,GAAG,WAAW,CAAC;YACjC,gBAAgB,EAAE,CAAC;YAEnB,IAAI,gBAAgB,IAAI,CAAC,EAAE;gBACvB,aAAa,CAAC,KAAI,CAAC,iBAAiB,CAAC,CAAC;gBACtC,KAAI,CAAC,iBAAiB,GAAG,IAAI,CAAA;gBAC7B,cAAc;gBACd,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACf,OAAM;aACT;YACD,KAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;QAChD,CAAC,EAAE,IAAI,CAAC,CAAC;IACb,CAAC;IAED,uDAAoB,GAApB,UAAqB,OAAe;QAChC,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,WAAI,OAAO,YAAI,CAAC;SACpD;IACL,CAAC;IAED,8BAA8B;IACtB,4DAAyB,GAAjC;QACI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,OAAO,CAAC,IAAI,CAAC,kEAAkE,CAAC,CAAC;YACjF,OAAO;SACV;QAED,kBAAkB;QAClB,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;QACtD,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;QAE9D,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE;YAClB,OAAO,CAAC,IAAI,CAAC,6DAA6D,EAAE;gBACxE,MAAM,EAAE,CAAC,CAAC,GAAG;gBACb,UAAU,EAAE,CAAC,CAAC,OAAO;aACxB,CAAC,CAAC;YACH,OAAO;SACV;QAED,qBAAqB;QACrB,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;QACzD,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC1B,OAAO,CAAC,IAAI,CAAC,mEAAmE,CAAC,CAAC;YAClF,OAAO;SACV;QAED,qBAAqB;QACrB,IAAI,CAAC,YAAY,CAAC;YACd,uBAAuB;YACvB,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC;YACpB,OAAO,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC,SAAS;YAEhC,mCAAmC;YACnC,IAAI,YAAY,GAAG,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ;YAE/D,SAAS;YACT,OAAO,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;YAErC,qBAAqB;YACrB,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC;YACpB,OAAO,CAAC,OAAO,GAAG,GAAG,CAAC;QAE1B,CAAC,EAAE,IAAI,CAAC,CAAC;IACb,CAAC;IAED,gCAAgC;IACxB,iEAA8B,GAAtC;QAAA,iBAkIC;QAjIG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,OAAO,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;YAC9D,OAAO;SACV;QAED,WAAW;QACX,IAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;QACvE,IAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;QAEhE,IAAI,CAAC,cAAc,IAAI,CAAC,WAAW,EAAE;YACjC,OAAO,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;YAC3D,OAAO;SACV;QAED,IAAM,KAAK,GAAG,WAAW,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;QACjD,IAAM,YAAY,GAAG,WAAW,CAAC,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC;QAE/D,IAAI,CAAC,KAAK,IAAI,CAAC,YAAY,EAAE;YACzB,OAAO,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;YACpD,OAAO;SACV;QAED,iBAAiB;QACjB,IAAI,yBAAyB,GAAG,EAAE,CAAC;QACnC,IAAI,2BAA2B,GAAG,EAAE,CAAC;QACrC,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,yBAAyB,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC;YAC7D,2BAA2B,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC;SACpE;QAED,sBAAsB;QACtB,aAAK,CAAC,aAAa,CAAC,cAAc;QAC9B,kBAAkB;QAClB,UAAC,IAAa;YACV,SAAS;YACT,aAAK,CAAC,kBAAkB,CAAC,IAAI,EAAE,eAAM,CAAC,eAAe,CAAC,CAAC;YAEvD,SAAS;YACT,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;YACpB,KAAK,CAAC,UAAU,GAAG,EAAE,CAAC;YACtB,IAAI,KAAK,GAAG,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC;YAC3B,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,eAAM,CAAC,oBAAoB,CAAC,CAAC;YACrD,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;YAC3B,WAAW,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE,SAAS,CAAC,CAAC;YAEhE,mBAAmB;YACnB,IAAI,KAAI,CAAC,kBAAkB,IAAI,KAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE;gBACzD,mBAAmB;gBACnB,KAAI,CAAC,kBAAkB,CAAC,QAAQ,GAAG,yBAAyB,GAAG,CAAC,CAAC;gBACjE,KAAI,CAAC,kBAAkB,CAAC,UAAU,GAAG,2BAA2B,GAAG,CAAC,CAAC;gBAErE,4BAA4B;gBAC5B,IAAM,gBAAgB,GAAG,KAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC;gBAC/E,IAAI,gBAAgB,EAAE;oBAClB,IAAI,YAAY,GAAG,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC;oBAClC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,EAAE,eAAM,CAAC,oBAAoB,CAAC,CAAC;oBAC5D,gBAAgB,CAAC,KAAK,GAAG,YAAY,CAAC;iBACzC;gBAED,SAAS;gBACT,KAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE,SAAS,CAAC,CAAC;aACpF;QACL,CAAC;QACD,kBAAkB;QAClB,UAAC,IAAa;YACV,SAAS;YACT,aAAK,CAAC,kBAAkB,CAAC,IAAI,EAAE,eAAM,CAAC,cAAc,CAAC,CAAC;YAEtD,SAAS;YACT,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;YACpB,KAAK,CAAC,UAAU,GAAG,EAAE,CAAC;YACtB,IAAI,KAAK,GAAG,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC;YAC3B,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,eAAM,CAAC,mBAAmB,CAAC,CAAC;YACpD,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;YAC3B,WAAW,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE,SAAS,CAAC,CAAC;YAEhE,mBAAmB;YACnB,IAAI,KAAI,CAAC,kBAAkB,IAAI,KAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE;gBACzD,cAAc;gBACd,KAAI,CAAC,kBAAkB,CAAC,QAAQ,GAAG,yBAAyB,CAAC;gBAC7D,KAAI,CAAC,kBAAkB,CAAC,UAAU,GAAG,2BAA2B,CAAC;gBAEjE,4BAA4B;gBAC5B,IAAM,gBAAgB,GAAG,KAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC;gBAC/E,IAAI,gBAAgB,EAAE;oBAClB,IAAI,YAAY,GAAG,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC;oBAClC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,EAAE,eAAM,CAAC,mBAAmB,CAAC,CAAC;oBAC3D,gBAAgB,CAAC,KAAK,GAAG,YAAY,CAAC;iBACzC;gBAED,SAAS;gBACT,KAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE,SAAS,CAAC,CAAC;aACpF;YAED,SAAS;YACT,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpB,CAAC;QACD,WAAW;QACX,UAAC,IAAa;YACV,SAAS;YACT,aAAK,CAAC,kBAAkB,CAAC,IAAI,EAAE,eAAM,CAAC,cAAc,CAAC,CAAC;YAEtD,SAAS;YACT,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;YACpB,KAAK,CAAC,UAAU,GAAG,EAAE,CAAC;YACtB,IAAI,KAAK,GAAG,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC;YAC3B,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,eAAM,CAAC,mBAAmB,CAAC,CAAC;YACpD,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;YAC3B,WAAW,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE,SAAS,CAAC,CAAC;YAEhE,mBAAmB;YACnB,IAAI,KAAI,CAAC,kBAAkB,IAAI,KAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE;gBACzD,cAAc;gBACd,KAAI,CAAC,kBAAkB,CAAC,QAAQ,GAAG,yBAAyB,CAAC;gBAC7D,KAAI,CAAC,kBAAkB,CAAC,UAAU,GAAG,2BAA2B,CAAC;gBAEjE,4BAA4B;gBAC5B,IAAM,gBAAgB,GAAG,KAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC;gBAC/E,IAAI,gBAAgB,EAAE;oBAClB,IAAI,YAAY,GAAG,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC;oBAClC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,EAAE,eAAM,CAAC,mBAAmB,CAAC,CAAC;oBAC3D,gBAAgB,CAAC,KAAK,GAAG,YAAY,CAAC;iBACzC;gBAED,SAAS;gBACT,KAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE,SAAS,CAAC,CAAC;aACpF;QACL,CAAC,CACJ,CAAC;IACN,CAAC;IAnUD;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACK;IAEvB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;gEACQ;IAE1B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACK;IAEvB;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;kEACW;IAE/B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;gEACgB;IAXjB,wBAAwB;QAD5C,OAAO;OACa,wBAAwB,CAuU5C;IAAD,+BAAC;CAvUD,AAuUC,CAvUqD,EAAE,CAAC,SAAS,GAuUjE;kBAvUoB,wBAAwB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\n\nimport { NoticeSettlement } from \"../bean/GameBean\";\nimport { GlobalBean } from \"../bean/GlobalBean\";\nimport { EventType } from \"../common/EventCenter\";\nimport { GameMgr } from \"../common/GameMgr\";\nimport { AutoMessageBean, AutoMessageId } from \"../net/MessageBaseBean\";\nimport CongratsItemController from \"../pfb/CongratsItemController\";\nimport { Config } from \"../util/Config\";\nimport { Tools } from \"../util/Tools\";\n\nconst { ccclass, property } = cc._decorator;\n\n//结算页面\n@ccclass\nexport default class CongratsDialogController extends cc.Component {\n\n    @property(cc.Node)\n    boardBg: cc.Node = null\n    @property(cc.Node)\n    contentLay: cc.Node = null\n    @property(cc.Node)\n    backBtn: cc.Node = null\n    @property(cc.Prefab)\n    congratsItem: cc.Prefab = null;//列表的 item\n    @property(cc.Node)\n    public layoutNode: cc.Node = null;//存放列表的布局\n\n\n    countdownTimeLabel: cc.Label = null\n    countdownInterval: number = null;//倒计时的 id\n\n    backCallback: Function = null //隐藏弹窗的回调\n    seconds: number = 10;//倒计时 10 秒\n\n\n    onLoad() {\n        this.countdownTimeLabel = this.backBtn.getChildByName('buttonLabel_time').getComponent(cc.Label);\n    }\n    protected onEnable(): void {\n        this.updateCountdownLabel(this.seconds);\n        // Tools.setCountDownTimeLabel(this.backBtn)\n    }\n\n    start() {\n        //backBtn 按钮点击事件\n        Tools.greenButton(this.backBtn, () => {\n            this.hide(true)\n        })\n        \n         // 设置倒计时标签的固定位置\n         this.setFixedCountdownPosition();  \n\n         // 设置整个按钮的点击效果（包括按钮背景和倒计时标签的统一反馈）\n         this.setupButtonWithCountdownEffect();\n    }\n\n\n    show(noticeSettlement: NoticeSettlement, backCallback: Function) {\n        this.backCallback = backCallback\n        this.node.active = true\n        this.boardBg.scale = 0\n\n         // 每次显示时重新设置倒计时标签的固定位置，确保位置正确\n         this.setFixedCountdownPosition();\n\n\n        this._setData(noticeSettlement)\n        // 执行缩放动画\n        cc.tween(this.boardBg)\n            .to(Config.dialogScaleTime, { scale: 1 })\n            .start();\n    }\n\n    _setData(noticeSettlement: NoticeSettlement) {\n        // 获取用户列表，优先使用 finalRanking，其次使用 users\n        let userList = noticeSettlement.finalRanking || noticeSettlement.users;\n\n        // 检查用户列表是否存在\n        if (!noticeSettlement || !userList || !Array.isArray(userList)) {\n            console.warn('NoticeSettlement 用户数据无效:', noticeSettlement);\n            this.startCountdown(10); // 仍然启动倒计时\n            return;\n        }\n\n        const currentUserId = GlobalBean.GetInstance().loginData.userInfo.userId;\n        const index = userList.findIndex((item) => item.userId === currentUserId);//搜索\n        if (index >= 0) {\n            // 对于扫雷游戏，finalRanking 中有 coinChg 字段，需要更新金币\n            if ('coin' in userList[index]) {\n                // UserSettlement 类型，直接使用 coin 字段\n                GlobalBean.GetInstance().loginData.userInfo.coin = (userList[index] as any).coin;\n            } else if ('coinChg' in userList[index]) {\n                // PlayerFinalResult 类型，使用 coinChg 字段更新金币\n                GlobalBean.GetInstance().loginData.userInfo.coin += (userList[index] as any).coinChg;\n            }\n        }\n\n        this.layoutNode.removeAllChildren();\n        for (let i = 0; i < userList.length; ++i) {\n            const item = cc.instantiate(this.congratsItem);\n            const data = userList[i];\n            this.layoutNode.addChild(item);\n            setTimeout(() => {\n                item.getComponent(CongratsItemController).createData(data, GlobalBean.GetInstance().noticeStartGame.users);\n            }, 100);\n        }\n        this.startCountdown(10)//倒计时 10 秒\n    }\n\n    // bool 在隐藏的时候是否返回大厅\n    hide(bool: boolean = false) {\n        if (this.backCallback) {\n            this.backCallback()\n        }\n        GameMgr.Console.Log('隐藏结算页面')\n        // 执行缩放动画\n        cc.tween(this.boardBg)\n            .to(Config.dialogScaleTime, { scale: 0 })\n            .call(() => {\n                this.node.active = false\n                if (bool) {\n                    GlobalBean.GetInstance().cleanData()\n                    let autoMessageBean: AutoMessageBean = {\n                        'msgId': AutoMessageId.JumpHallPage,//跳转进大厅页面\n                        'data': { 'type': 2 }//2是结算弹窗跳转的\n                    }\n                    GameMgr.Event.Send(EventType.AutoMessage, autoMessageBean);\n                }\n            })\n            .start();\n    }\n    protected onDisable(): void {\n        if (this.countdownInterval) {\n            clearInterval(this.countdownInterval);\n        }\n\n    }\n\n    startCountdown(seconds: number) {\n        // 在开始倒计时前再次确保位置正确\n        this.setFixedCountdownPosition();\n        let remainingSeconds = seconds;\n        this.updateCountdownLabel(remainingSeconds);\n\n        this.countdownInterval = setInterval(() => {\n            remainingSeconds--;\n\n            if (remainingSeconds <= 0) {\n                clearInterval(this.countdownInterval);\n                this.countdownInterval = null\n                // 倒计时结束时的处理逻辑\n                this.hide(true)\n                return\n            }\n            this.updateCountdownLabel(remainingSeconds);\n        }, 1000);\n    }\n\n    updateCountdownLabel(seconds: number) {\n        if (this.countdownTimeLabel) {\n            this.countdownTimeLabel.string = `（${seconds}s）`;\n        }\n    }\n\n    // 设置固定的倒计时位置，左括号始终对准back文字的右边\n    private setFixedCountdownPosition() {\n        if (!this.backBtn) {\n            console.warn(\"CongratsDialogController: setFixedCountdownPosition - backBtn不存在\");\n            return;\n        }\n\n        // 重新获取节点，确保引用是最新的\n        let btn = this.backBtn.getChildByName('button_label');\n        let timeBtn = this.backBtn.getChildByName('buttonLabel_time');\n\n        if (!btn || !timeBtn) {\n            console.warn(\"CongratsDialogController: setFixedCountdownPosition - 缺少子节点\", {\n                hasBtn: !!btn,\n                hasTimeBtn: !!timeBtn\n            });\n            return;\n        }\n\n        // 重新获取倒计时标签组件，确保引用正确\n        this.countdownTimeLabel = timeBtn.getComponent(cc.Label);\n        if (!this.countdownTimeLabel) {\n            console.warn(\"CongratsDialogController: setFixedCountdownPosition - 无法获取Label组件\");\n            return;\n        }\n\n        // 延迟一帧执行，确保所有UI初始化完成\n        this.scheduleOnce(() => {\n            // 强制设置锚点为左对齐，确保左括号位置固定\n            timeBtn.anchorX = 0;\n            timeBtn.anchorY = 0.5; // 确保垂直居中\n\n            // 计算固定位置：back文字右边缘 + 小间距，左括号固定在此位置\n            let fixedLeftPos = btn.position.x + btn.width / 2 + 5; // 5像素间距\n\n            // 直接设置位置\n            timeBtn.setPosition(fixedLeftPos, 0);\n\n            // 再次强制设置锚点，防止被其他代码重置\n            timeBtn.anchorX = 0;\n            timeBtn.anchorY = 0.5;\n\n        }, 0.01);\n    }\n\n    // 设置整个按钮的统一点击效果（按钮背景和倒计时标签都有反馈）\n    private setupButtonWithCountdownEffect() {\n        if (!this.backBtn) {\n            console.warn(\"CongratsDialogController: backBtn未设置，无法添加点击效果\");\n            return;\n        }\n\n        // 获取按钮的子节点\n        const btnColorNormal = this.backBtn.getChildByName('btn_color_normal');\n        const buttonLabel = this.backBtn.getChildByName('button_label');\n\n        if (!btnColorNormal || !buttonLabel) {\n            console.warn(\"CongratsDialogController: 按钮结构不完整，无法添加点击效果\");\n            return;\n        }\n\n        const label = buttonLabel.getComponent(cc.Label);\n        const labelOutline = buttonLabel.getComponent(cc.LabelOutline);\n\n        if (!label || !labelOutline) {\n            console.warn(\"CongratsDialogController: 按钮标签组件不完整\");\n            return;\n        }\n\n        // 记录倒计时标签的原始字体大小\n        let originalCountdownFontSize = 36;\n        let originalCountdownLineHeight = 36;\n        if (this.countdownTimeLabel) {\n            originalCountdownFontSize = this.countdownTimeLabel.fontSize;\n            originalCountdownLineHeight = this.countdownTimeLabel.lineHeight;\n        }\n\n        // 自定义按钮点击效果，同时控制倒计时标签\n        Tools.setTouchEvent(btnColorNormal,\n            // 按下时：按钮和倒计时标签都变暗\n            (node: cc.Node) => {\n                // 按钮背景变暗\n                Tools.setNodeSpriteFrame(node, Config.btnGreenPressed);\n\n                // 按钮文字变暗\n                label.fontSize = 34;\n                label.lineHeight = 34;\n                let color = new cc.Color();\n                cc.Color.fromHEX(color, Config.btnGreenPressedColor);\n                labelOutline.color = color;\n                buttonLabel.color = cc.Color.fromHEX(new cc.Color(), '#B3B3B3');\n\n                // 倒计时标签也变暗（字体缩小效果）\n                if (this.countdownTimeLabel && this.countdownTimeLabel.node) {\n                    // 字体大小缩小（从原始大小缩小2）\n                    this.countdownTimeLabel.fontSize = originalCountdownFontSize - 2;\n                    this.countdownTimeLabel.lineHeight = originalCountdownLineHeight - 2;\n\n                    // 轮廓颜色变化（如果有LabelOutline组件）\n                    const countdownOutline = this.countdownTimeLabel.getComponent(cc.LabelOutline);\n                    if (countdownOutline) {\n                        let outlineColor = new cc.Color();\n                        cc.Color.fromHEX(outlineColor, Config.btnGreenPressedColor);\n                        countdownOutline.color = outlineColor;\n                    }\n\n                    // 文字颜色变暗\n                    this.countdownTimeLabel.node.color = cc.Color.fromHEX(new cc.Color(), '#B3B3B3');\n                }\n            },\n            // 抬起时：恢复正常并执行点击逻辑\n            (node: cc.Node) => {\n                // 按钮背景恢复\n                Tools.setNodeSpriteFrame(node, Config.btnGreenNormal);\n\n                // 按钮文字恢复\n                label.fontSize = 36;\n                label.lineHeight = 36;\n                let color = new cc.Color();\n                cc.Color.fromHEX(color, Config.btnGreenNormalColor);\n                labelOutline.color = color;\n                buttonLabel.color = cc.Color.fromHEX(new cc.Color(), '#FFFFFF');\n\n                // 倒计时标签恢复（恢复到原始大小）\n                if (this.countdownTimeLabel && this.countdownTimeLabel.node) {\n                    // 字体大小恢复到原始大小\n                    this.countdownTimeLabel.fontSize = originalCountdownFontSize;\n                    this.countdownTimeLabel.lineHeight = originalCountdownLineHeight;\n\n                    // 轮廓颜色恢复（如果有LabelOutline组件）\n                    const countdownOutline = this.countdownTimeLabel.getComponent(cc.LabelOutline);\n                    if (countdownOutline) {\n                        let outlineColor = new cc.Color();\n                        cc.Color.fromHEX(outlineColor, Config.btnGreenNormalColor);\n                        countdownOutline.color = outlineColor;\n                    }\n\n                    // 文字颜色恢复\n                    this.countdownTimeLabel.node.color = cc.Color.fromHEX(new cc.Color(), '#FFFFFF');\n                }\n\n                // 执行点击逻辑\n                this.hide(true);\n            },\n            // 取消时：恢复正常\n            (node: cc.Node) => {\n                // 按钮背景恢复\n                Tools.setNodeSpriteFrame(node, Config.btnGreenNormal);\n\n                // 按钮文字恢复\n                label.fontSize = 36;\n                label.lineHeight = 36;\n                let color = new cc.Color();\n                cc.Color.fromHEX(color, Config.btnGreenNormalColor);\n                labelOutline.color = color;\n                buttonLabel.color = cc.Color.fromHEX(new cc.Color(), '#FFFFFF');\n\n                // 倒计时标签恢复（恢复到原始大小）\n                if (this.countdownTimeLabel && this.countdownTimeLabel.node) {\n                    // 字体大小恢复到原始大小\n                    this.countdownTimeLabel.fontSize = originalCountdownFontSize;\n                    this.countdownTimeLabel.lineHeight = originalCountdownLineHeight;\n\n                    // 轮廓颜色恢复（如果有LabelOutline组件）\n                    const countdownOutline = this.countdownTimeLabel.getComponent(cc.LabelOutline);\n                    if (countdownOutline) {\n                        let outlineColor = new cc.Color();\n                        cc.Color.fromHEX(outlineColor, Config.btnGreenNormalColor);\n                        countdownOutline.color = outlineColor;\n                    }\n\n                    // 文字颜色恢复\n                    this.countdownTimeLabel.node.color = cc.Color.fromHEX(new cc.Color(), '#FFFFFF');\n                }\n            }\n        );\n    }\n}\n"]}